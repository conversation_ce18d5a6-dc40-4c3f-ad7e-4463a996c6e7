/* Auto-generated custom theme CSS */
:root {
    --primary-color: #000000;
    --secondary-color: #a04646;
    --accent-color: #ffffff;
    --sidebar-bg-color: #ffffff;
    --sidebar-text-color: #000000;
    --sidebar-hover-color: #cfd2fc;
    --header-bg-color: #000000;
    --header-text-color: #ffffff;
    --header-icon-color: #ffffff;
    --header-button-color: #a04646;
    --primary-rgb: 0, 0, 0;
    --secondary-rgb: 160, 70, 70;
    --accent-rgb: 255, 255, 255;
    --sidebar-bg-rgb: 255, 255, 255;
    --sidebar-text-rgb: 0, 0, 0;
    --sidebar-hover-rgb: 207, 210, 252;
}

/* Header and Navigation */
.header {
    background-color: var(--header-bg-color) !important;
    color: var(--header-text-color) !important;
}

.header h1 {
    color: var(--header-text-color) !important;
}

.header .user-info {
    color: var(--header-text-color) !important;
}

.header .user-info i {
    color: var(--header-icon-color) !important;
}

.header .user-info button {
    background-color: var(--header-button-color) !important;
    color: var(--header-text-color) !important;
    border-color: var(--header-button-color) !important;
}

.header .user-info button:hover {
    background-color: var(--header-text-color) !important;
    color: var(--header-bg-color) !important;
}

#sidebar-toggle {
    background-color: var(--header-button-color) !important;
    color: var(--header-text-color) !important;
    border-color: var(--header-button-color) !important;
}

.sidebar {
    background-color: var(--sidebar-bg-color) !important;
}

.sidebar nav ul li a {
    color: var(--sidebar-text-color) !important;
}

.sidebar nav ul li a:hover,
.sidebar nav ul li a.active {
    color: var(--sidebar-text-color) !important;
    background-color: var(--sidebar-hover-color) !important;
}

/* Buttons */
.btn-primary {
    background-color: var(--secondary-color) !important;
    border-color: var(--secondary-color) !important;
}

.btn-primary:hover {
    background-color: var(--accent-color) !important;
    border-color: var(--accent-color) !important;
}

/* Links */
a {
    color: var(--accent-color) !important;
}

/* Cards */
.card-header {
    background-color: rgba(var(--primary-rgb), 0.1) !important;
    border-bottom-color: var(--primary-color) !important;
}

/* Tables */
.table thead th {
    background-color: var(--primary-color) !important;
    color: white !important;
}

/* Stats Cards */
.stat-card.primary {
    border-top-color: var(--primary-color) !important;
}

.stat-card .stat-icon {
    color: var(--primary-color) !important;
}

/* Badges */
.bg-primary {
    background-color: var(--primary-color) !important;
}

.bg-secondary {
    background-color: var(--secondary-color) !important;
}
