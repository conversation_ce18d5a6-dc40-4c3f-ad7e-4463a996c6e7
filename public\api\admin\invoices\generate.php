<?php
// Define BASE_PATH if not already defined
if (!defined('BASE_PATH')) {
    define('BASE_PATH', dirname(__DIR__, 4));
}

// Register Composer autoloader
require_once BASE_PATH . '/vendor/autoload.php';

use App\Core\App;
use App\Core\Database;
use App\Core\Session;

// Initialize the application
$app = App::getInstance();

// Set appropriate headers
if (isset($_POST['download']) && $_POST['download'] === '1') {
    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename="invoice.pdf"');
} else {
    header('Content-Type: application/json');
}

header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Check if user is authenticated as admin
if (!Session::has('admin_user_id')) {
    http_response_code(401);
    if (!isset($_POST['download'])) {
        echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    }
    exit;
}

try {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Get form data
        $shipment_id = $_POST['shipment_id'] ?? null;
        $invoice_design = $_POST['invoice_design'] ?? 'classic';
        $invoice_type = $_POST['invoice_type'] ?? 'standard';
        $currency = $_POST['currency'] ?? 'EUR';
        $tax_rate = floatval($_POST['tax_rate'] ?? 24) / 100; // Convert percentage to decimal
        $include_logo = isset($_POST['include_logo']) ? true : false;
        $include_terms = isset($_POST['include_terms']) ? true : false;
        $payment_terms = $_POST['payment_terms'] ?? 'Net 30';
        $notes = $_POST['notes'] ?? '';
        
        // Get additional charges
        $additional_charges = [];
        if (isset($_POST['charge_description']) && is_array($_POST['charge_description'])) {
            for ($i = 0; $i < count($_POST['charge_description']); $i++) {
                if (!empty($_POST['charge_description'][$i]) && !empty($_POST['charge_amount'][$i])) {
                    $additional_charges[] = [
                        'description' => $_POST['charge_description'][$i],
                        'amount' => floatval($_POST['charge_amount'][$i])
                    ];
                }
            }
        }
        
        if (!$shipment_id) {
            if (isset($_POST['download'])) {
                http_response_code(400);
                echo "Shipment ID is required";
                exit;
            } else {
                echo json_encode(['success' => false, 'message' => 'Shipment ID is required']);
                exit;
            }
        }
        
        // Get shipment data
        Database::prepare("SELECT * FROM shipments WHERE id = :id");
        Database::bindValue(':id', $shipment_id, PDO::PARAM_INT);
        Database::execute();
        $shipment = Database::fetch();
        
        if (!$shipment) {
            if (isset($_POST['download'])) {
                http_response_code(404);
                echo "Shipment not found";
                exit;
            } else {
                echo json_encode(['success' => false, 'message' => 'Shipment not found']);
                exit;
            }
        }
        
        // Get currency symbol
        $currencySymbols = [
            'USD' => '$', 'EUR' => '€', 'GBP' => '£', 'CAD' => 'C$', 'AUD' => 'A$',
            'JPY' => '¥', 'CHF' => 'CHF', 'SEK' => 'kr', 'NOK' => 'kr', 'DKK' => 'kr'
        ];
        $currencySymbol = $currencySymbols[$currency] ?? $currency;

        // Calculate totals
        $shipping_cost = floatval($shipment['total_freight'] ?? $shipment['shipping_cost'] ?? 0);
        $additional_total = array_sum(array_column($additional_charges, 'amount'));
        $subtotal = $shipping_cost + $additional_total;
        $tax_amount = $subtotal * $tax_rate;
        $total_amount = $subtotal + $tax_amount;
        
        // Generate invoice HTML
        $invoiceHtml = generateInvoiceHtml($shipment, [
            'invoice_design' => $invoice_design,
            'invoice_type' => $invoice_type,
            'currency' => $currency,
            'currency_symbol' => $currencySymbol,
            'tax_rate' => $tax_rate * 100, // Convert back to percentage for display
            'include_logo' => $include_logo,
            'include_terms' => $include_terms,
            'payment_terms' => $payment_terms,
            'notes' => $notes,
            'additional_charges' => $additional_charges,
            'shipping_cost' => $shipping_cost,
            'subtotal' => $subtotal,
            'tax_amount' => $tax_amount,
            'total_amount' => $total_amount
        ]);
        
        if (isset($_POST['download']) && $_POST['download'] === '1') {
            // Generate PDF and return as download
            $pdf = generateInvoicePdf($invoiceHtml, $shipment);
            echo $pdf;
        } else {
            // Return HTML for preview
            echo json_encode([
                'success' => true,
                'html' => $invoiceHtml,
                'shipment_id' => $shipment_id,
                'tracking_number' => $shipment['tracking_number'],
                'total_amount' => $total_amount
            ]);
        }
        
    } else {
        http_response_code(405);
        if (!isset($_POST['download'])) {
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
        }
    }
    
} catch (Exception $e) {
    error_log("Error in invoice generation API: " . $e->getMessage());
    http_response_code(500);
    if (isset($_POST['download'])) {
        echo "Error generating invoice: " . $e->getMessage();
    } else {
        echo json_encode(['success' => false, 'message' => 'Error generating invoice: ' . $e->getMessage()]);
    }
}

function generateInvoiceHtml($shipment, $options) {
    $invoice_design = $options['invoice_design'] ?? 'classic';
    $invoice_number = 'INV-' . date('Y') . '-' . str_pad($shipment['id'], 6, '0', STR_PAD_LEFT);
    $invoice_date = date('Y-m-d');
    $due_date = date('Y-m-d', strtotime('+30 days'));

    // Route to appropriate design function
    switch ($invoice_design) {
        case 'modern':
            return generateModernInvoiceHtml($shipment, $options, $invoice_number, $invoice_date, $due_date);
        case 'minimal':
            return generateMinimalInvoiceHtml($shipment, $options, $invoice_number, $invoice_date, $due_date);
        case 'classic':
        default:
            return generateClassicInvoiceHtml($shipment, $options, $invoice_number, $invoice_date, $due_date);
    }
}

function generateClassicInvoiceHtml($shipment, $options, $invoice_number, $invoice_date, $due_date) {

    // Get appearance settings
    $appearanceSettings = [];
    try {
        Database::prepare("SELECT name, value FROM settings WHERE category = 'appearance'");
        Database::execute();
        $results = Database::fetchAll();

        foreach ($results as $setting) {
            $appearanceSettings[$setting['name']] = $setting['value'];
        }
    } catch (Exception $e) {
        // Use defaults if settings not found
    }

    // Set default colors if not found in settings
    $primaryColor = $appearanceSettings['primary_color'] ?? '#2c3e50';
    $secondaryColor = $appearanceSettings['secondary_color'] ?? '#e74c3c';
    $accentColor = $appearanceSettings['accent_color'] ?? '#3498db';

    // Get company logo from settings
    $logoHtml = '';
    if (!empty($appearanceSettings['company_logo'])) {
        $logoPath = $appearanceSettings['company_logo'];
        $logoUrl = $logoPath;
        $logoHtml = '<img src="' . $logoUrl . '" alt="Company Logo" style="max-height: 60px; max-width: 200px; margin-bottom: 10px;">';
    }

    $html = '
    <div class="invoice" style="width: 100%; max-width: 800px; margin: 0 auto; font-family: Arial, sans-serif; background: white; padding: 0; border: 1px solid ' . $primaryColor . '; border-radius: 8px; overflow: hidden;">
        <!-- Header Section -->
        <div class="invoice-header" style="background: ' . $primaryColor . '; color: white; padding: 20px; border-bottom: 1px solid ' . $primaryColor . '; display: flex; justify-content: space-between; align-items: center;">
            <div class="company-info">
                ' . ($logoHtml ? $logoHtml : '
                <div class="logo" style="font-size: 32px; font-weight: bold; color: white; margin-bottom: 5px;">
                    <span style="background: ' . $secondaryColor . '; color: white; padding: 8px 15px; border-radius: 3px;">ELTA</span>
                    <span style="color: white; margin-left: 8px;">COURIER</span>
                </div>
                ') . '
                <div style="font-size: 14px; color: rgba(255,255,255,0.8); margin-bottom: 5px;">The Bridge To Your Logistics Success</div>
                <div style="font-size: 12px; color: rgba(255,255,255,0.8);">
                    Athens, Greece | +30 210 1234567<br>
                    www.eltacourier.com | <EMAIL>
                </div>
            </div>
            <div class="invoice-details" style="text-align: right;">
                <h1 style="color: white; margin: 0; font-size: 36px; font-weight: bold;">INVOICE</h1>
                <div style="margin-top: 10px; font-size: 14px;">
                    <div style="margin-bottom: 5px;"><strong>Invoice Number:</strong> ' . $invoice_number . '</div>
                    <div style="margin-bottom: 5px;"><strong>Invoice Date:</strong> ' . $invoice_date . '</div>
                    <div style="margin-bottom: 5px;"><strong>Due Date:</strong> ' . $due_date . '</div>
                    <div><strong>Payment Terms:</strong> Net 30</div>
                </div>
            </div>
        </div>

        <!-- Billing Information -->
        <div class="billing-section" style="display: flex; border-bottom: 1px solid ' . $primaryColor . '; border-top: 1px solid ' . $primaryColor . ';">
            <!-- Bill To -->
            <div class="bill-to-section" style="width: 50%; padding: 20px; border-right: 1px solid ' . $primaryColor . ';">
                <div class="section-title" style="background: ' . $secondaryColor . '; color: white; padding: 8px 12px; margin: -20px -20px 15px -20px; font-weight: bold; font-size: 14px; border-bottom: 1px solid #000;">
                    BILL TO
                </div>
                <div class="billing-info" style="font-size: 12px; line-height: 1.6;">
                    <div style="margin-bottom: 8px;"><strong>Company:</strong> ' . htmlspecialchars($shipment['shipper_name'] ?? 'N/A') . '</div>
                    <div style="margin-bottom: 8px;"><strong>Address:</strong></div>
                    <div style="margin-left: 10px; margin-bottom: 8px;">' . nl2br(htmlspecialchars($shipment['shipper_address'] ?? 'N/A')) . '</div>
                    <div style="margin-bottom: 8px;"><strong>Phone:</strong> ' . htmlspecialchars($shipment['shipper_phone'] ?? 'N/A') . '</div>
                    <div style="margin-bottom: 8px;"><strong>Email:</strong> ' . htmlspecialchars($shipment['shipper_email'] ?? 'N/A') . '</div>
                </div>
            </div>

            <!-- Ship To -->
            <div class="ship-to-section" style="width: 50%; padding: 20px;">
                <div class="section-title" style="background: #e74c3c; color: white; padding: 8px 12px; margin: -20px -20px 15px -20px; font-weight: bold; font-size: 14px; border-bottom: 1px solid #000;">
                    SHIP TO
                </div>
                <div class="shipping-info" style="font-size: 12px; line-height: 1.6;">
                    <div style="margin-bottom: 8px;"><strong>Recipient:</strong> ' . htmlspecialchars($shipment['receiver_name'] ?? 'N/A') . '</div>
                    <div style="margin-bottom: 8px;"><strong>Address:</strong></div>
                    <div style="margin-left: 10px; margin-bottom: 8px;">' . nl2br(htmlspecialchars($shipment['receiver_address'] ?? 'N/A')) . '</div>
                    <div style="margin-bottom: 8px;"><strong>Phone:</strong> ' . htmlspecialchars($shipment['receiver_phone'] ?? 'N/A') . '</div>
                    <div style="margin-bottom: 8px;"><strong>Email:</strong> ' . htmlspecialchars($shipment['receiver_email'] ?? 'N/A') . '</div>
                </div>
            </div>
        </div>
        
        <div class="shipment-details" style="margin-bottom: 30px; padding: 20px; background: #f9f9f9; border-radius: 5px; border: 2px solid ' . $primaryColor . ';">
            <h3 style="color: #333; margin-bottom: 15px; border-bottom: 1px solid #ddd; padding-bottom: 5px;">Shipment Details</h3>
            <div style="display: flex; justify-content: space-between; font-size: 14px;">
                <div>
                    <strong>Tracking Number:</strong> ' . htmlspecialchars($shipment['tracking_number']) . '<br>
                    <strong>Service Type:</strong> ' . htmlspecialchars($shipment['shipment_type'] ?? 'Standard') . '<br>
                    <strong>Weight:</strong> ' . htmlspecialchars($shipment['total_weight'] ?? 'N/A') . ' kg
                </div>
                <div>
                    <strong>Status:</strong> ' . htmlspecialchars($shipment['status']) . '<br>
                    <strong>Ship Date:</strong> ' . date('Y-m-d', strtotime($shipment['created_at'])) . '<br>
                    <strong>Payment Terms:</strong> ' . htmlspecialchars($options['payment_terms']) . '
                </div>
            </div>
        </div>
        
        <table style="width: 100%; border-collapse: collapse; margin-bottom: 30px; border: 1px solid ' . $primaryColor . ';">
            <thead>
                <tr style="background: #333; color: white;">
                    <th style="padding: 12px; text-align: left; border: 1px solid #000;">Description</th>
                    <th style="padding: 12px; text-align: right; border: 1px solid #000;">Amount</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td style="padding: 12px; border: 1px solid #000;">Shipping Service - ' . htmlspecialchars($shipment['shipment_type'] ?? 'Standard') . '</td>
                    <td style="padding: 12px; text-align: right; border: 1px solid #000;">' . $options['currency_symbol'] . number_format($options['shipping_cost'], 2) . '</td>
                </tr>';
    
    // Add additional charges
    foreach ($options['additional_charges'] as $charge) {
        $html .= '
                <tr>
                    <td style="padding: 12px; border: 1px solid #000;">' . htmlspecialchars($charge['description']) . '</td>
                    <td style="padding: 12px; text-align: right; border: 1px solid #000;">' . $options['currency_symbol'] . number_format($charge['amount'], 2) . '</td>
                </tr>';
    }
    
    $html .= '
            </tbody>
            <tfoot>
                <tr>
                    <td style="padding: 12px; text-align: right; border: 1px solid #000; font-weight: bold;">Subtotal:</td>
                    <td style="padding: 12px; text-align: right; border: 1px solid #000; font-weight: bold;">' . $options['currency_symbol'] . number_format($options['subtotal'], 2) . '</td>
                </tr>
                <tr>
                    <td style="padding: 12px; text-align: right; border: 1px solid #000;">Tax (' . number_format($options['tax_rate'], 1) . '%):</td>
                    <td style="padding: 12px; text-align: right; border: 1px solid #000;">' . $options['currency_symbol'] . number_format($options['tax_amount'], 2) . '</td>
                </tr>
                <tr style="background: #f0f0f0;">
                    <td style="padding: 12px; text-align: right; border: 1px solid #000; font-weight: bold; font-size: 16px;">Total:</td>
                    <td style="padding: 12px; text-align: right; border: 1px solid #000; font-weight: bold; font-size: 16px;">' . $options['currency_symbol'] . number_format($options['total_amount'], 2) . '</td>
                </tr>
            </tfoot>
        </table>';
    
    if (!empty($options['notes'])) {
        $html .= '
        <div class="notes" style="margin-bottom: 30px;">
            <h3 style="color: #333; margin-bottom: 10px;">Notes:</h3>
            <div style="font-size: 14px; padding: 15px; background: #f9f9f9; border-radius: 5px;">
                ' . nl2br(htmlspecialchars($options['notes'])) . '
            </div>
        </div>';
    }
    
    if ($options['include_terms']) {
        $html .= '
        <div class="terms" style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 12px; color: #666;">
            <h4 style="color: #333; margin-bottom: 10px;">Terms and Conditions:</h4>
            <ul style="margin: 0; padding-left: 20px;">
                <li>Payment is due within ' . htmlspecialchars($options['payment_terms']) . ' of invoice date.</li>
                <li>Late payments may be subject to a 1.5% monthly service charge.</li>
                <li>All shipments are insured up to $100 unless additional insurance is purchased.</li>
                <li>Claims for damaged or lost items must be reported within 30 days.</li>
                <li>ELTA Courier is not responsible for delays due to weather or customs.</li>
            </ul>
        </div>';
    }
    
    $html .= '
    </div>';
    
    return $html;
}

function generateModernInvoiceHtml($shipment, $options, $invoice_number, $invoice_date, $due_date) {
    // Get appearance settings
    $appearanceSettings = [];
    try {
        Database::prepare("SELECT name, value FROM settings WHERE category = 'appearance'");
        Database::execute();
        $results = Database::fetchAll();

        foreach ($results as $setting) {
            $appearanceSettings[$setting['name']] = $setting['value'];
        }
    } catch (Exception $e) {
        // Use defaults if settings not found
    }

    $primaryColor = $appearanceSettings['primary_color'] ?? '#2c3e50';
    $secondaryColor = $appearanceSettings['secondary_color'] ?? '#3498db';
    $logoPath = $appearanceSettings['company_logo'] ?? '';

    // Logo HTML
    $logoHtml = '';
    if ($options['include_logo'] && !empty($logoPath) && file_exists('public/uploads/logos/' . $logoPath)) {
        $logoHtml = '<img src="/courier/public/uploads/logos/' . $logoPath . '" alt="Company Logo" style="max-height: 60px; max-width: 200px; object-fit: contain;">';
    }

    $html = '
    <div class="invoice modern-design" style="width: 100%; max-width: 800px; margin: 0 auto; font-family: \'Segoe UI\', Tahoma, Geneva, Verdana, sans-serif; background: white; padding: 0; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        <!-- Header Section -->
        <div class="invoice-header" style="background: linear-gradient(135deg, ' . $primaryColor . ' 0%, ' . $secondaryColor . ' 100%); color: white; padding: 30px; display: flex; justify-content: space-between; align-items: center;">
            <div class="company-info">
                ' . ($logoHtml ? $logoHtml : '
                <div class="logo" style="font-size: 28px; font-weight: 300; color: white; margin-bottom: 5px;">
                    <span style="background: rgba(255,255,255,0.2); color: white; padding: 8px 15px; border-radius: 6px; font-weight: 600;">ELTA</span>
                </div>') . '
                <div style="font-size: 14px; opacity: 0.9; margin-top: 8px;">Professional Courier Services</div>
            </div>
            <div class="invoice-info" style="text-align: right;">
                <h1 style="margin: 0; font-size: 32px; font-weight: 300; letter-spacing: 1px;">INVOICE</h1>
                <div style="font-size: 16px; margin-top: 8px; opacity: 0.9;">' . htmlspecialchars($invoice_number) . '</div>
            </div>
        </div>

        <!-- Content Section -->
        <div class="invoice-content" style="padding: 40px;">
            <!-- Invoice Details Table -->
            <table style="width: 100%; margin-bottom: 30px; border-collapse: separate; border-spacing: 0; border: 1px solid #e1e5e9; border-radius: 8px; overflow: hidden;">
                <thead>
                    <tr style="background: #f8f9fa;">
                        <th style="padding: 15px; text-align: left; font-weight: 600; color: #495057; border-bottom: 1px solid #e1e5e9;">Invoice Details</th>
                        <th style="padding: 15px; text-align: left; font-weight: 600; color: #495057; border-bottom: 1px solid #e1e5e9;">Shipment Information</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="padding: 15px; border-bottom: 1px solid #e1e5e9; vertical-align: top;">
                            <div style="margin-bottom: 8px;"><strong>Invoice Date:</strong> ' . htmlspecialchars($invoice_date) . '</div>
                            <div style="margin-bottom: 8px;"><strong>Due Date:</strong> ' . htmlspecialchars($due_date) . '</div>
                            <div><strong>Payment Terms:</strong> ' . htmlspecialchars($options['payment_terms']) . '</div>
                        </td>
                        <td style="padding: 15px; border-bottom: 1px solid #e1e5e9; vertical-align: top;">
                            <div style="margin-bottom: 8px;"><strong>Tracking Number:</strong> ' . htmlspecialchars($shipment['tracking_number']) . '</div>
                            <div style="margin-bottom: 8px;"><strong>Service Type:</strong> ' . htmlspecialchars($shipment['service_type'] ?? 'Standard') . '</div>
                            <div><strong>Route:</strong> ' . htmlspecialchars($shipment['origin']) . ' → ' . htmlspecialchars($shipment['destination']) . '</div>
                        </td>
                    </tr>
                </tbody>
            </table>

            <!-- Client Information -->
            <div style="display: flex; justify-content: space-between; margin-bottom: 30px;">
                <div style="flex: 1; margin-right: 20px;">
                    <h3 style="color: ' . $primaryColor . '; margin-bottom: 15px; font-size: 18px; font-weight: 600;">Bill To:</h3>
                    <div style="background: #f8f9fa; padding: 20px; border: 1px solid #e9ecef;">
                        <div style="font-weight: 600; margin-bottom: 8px;">' . htmlspecialchars($shipment['sender_name'] ?? $shipment['shipper_name'] ?? 'N/A') . '</div>
                        <div style="color: #6c757d; line-height: 1.5;">
                            ' . htmlspecialchars($shipment['sender_address'] ?? $shipment['shipper_address'] ?? 'N/A') . '<br>
                            ' . htmlspecialchars($shipment['sender_city'] ?? $shipment['shipper_city'] ?? '') . ', ' . htmlspecialchars($shipment['sender_country'] ?? $shipment['shipper_country'] ?? '') . '<br>
                            ' . htmlspecialchars($shipment['sender_phone'] ?? $shipment['shipper_phone'] ?? '') . '
                        </div>
                    </div>
                </div>
                <div style="flex: 1;">
                    <h3 style="color: ' . $primaryColor . '; margin-bottom: 15px; font-size: 18px; font-weight: 600;">Ship To:</h3>
                    <div style="background: #f8f9fa; padding: 20px; border: 1px solid #e9ecef;">
                        <div style="font-weight: 600; margin-bottom: 8px;">' . htmlspecialchars($shipment['receiver_name'] ?? 'N/A') . '</div>
                        <div style="color: #6c757d; line-height: 1.5;">
                            ' . htmlspecialchars($shipment['receiver_address'] ?? 'N/A') . '<br>
                            ' . htmlspecialchars($shipment['receiver_city'] ?? '') . ', ' . htmlspecialchars($shipment['receiver_country'] ?? '') . '<br>
                            ' . htmlspecialchars($shipment['receiver_phone'] ?? '') . '
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charges Table -->
            <table style="width: 100%; border-collapse: separate; border-spacing: 0; border: 1px solid #e1e5e9; border-radius: 8px; overflow: hidden; margin-bottom: 30px;">
                <thead>
                    <tr style="background: ' . $primaryColor . '; color: white;">
                        <th style="padding: 15px; text-align: left; font-weight: 600;">Description</th>
                        <th style="padding: 15px; text-align: right; font-weight: 600;">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    <tr style="background: #fff;">
                        <td style="padding: 15px; border-bottom: 1px solid #e1e5e9;">Shipping Cost</td>
                        <td style="padding: 15px; text-align: right; border-bottom: 1px solid #e1e5e9;">' . $options['currency_symbol'] . number_format($options['shipping_cost'], 2) . '</td>
                    </tr>';

    // Add additional charges
    foreach ($options['additional_charges'] as $charge) {
        $html .= '
                    <tr style="background: #fff;">
                        <td style="padding: 15px; border-bottom: 1px solid #e1e5e9;">' . htmlspecialchars($charge['description']) . '</td>
                        <td style="padding: 15px; text-align: right; border-bottom: 1px solid #e1e5e9;">' . $options['currency_symbol'] . number_format($charge['amount'], 2) . '</td>
                    </tr>';
    }

    $html .= '
                    <tr style="background: #f8f9fa;">
                        <td style="padding: 15px; font-weight: 600;">Subtotal</td>
                        <td style="padding: 15px; text-align: right; font-weight: 600;">' . $options['currency_symbol'] . number_format($options['subtotal'], 2) . '</td>
                    </tr>
                    <tr style="background: #f8f9fa;">
                        <td style="padding: 15px;">Tax (' . $options['tax_rate'] . '%)</td>
                        <td style="padding: 15px; text-align: right;">' . $options['currency_symbol'] . number_format($options['tax_amount'], 2) . '</td>
                    </tr>
                    <tr style="background: ' . $primaryColor . '; color: white;">
                        <td style="padding: 20px; font-size: 18px; font-weight: 700;">TOTAL</td>
                        <td style="padding: 20px; text-align: right; font-size: 18px; font-weight: 700;">' . $options['currency_symbol'] . number_format($options['total_amount'], 2) . '</td>
                    </tr>
                </tbody>
            </table>';

    // Add notes if provided
    if (!empty($options['notes'])) {
        $html .= '
        <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; margin-bottom: 30px;">
            <h4 style="color: #856404; margin-bottom: 10px; font-size: 16px;">Notes:</h4>
            <p style="margin: 0; color: #856404; line-height: 1.5;">' . nl2br(htmlspecialchars($options['notes'])) . '</p>
        </div>';
    }

    // Add terms if enabled
    if ($options['include_terms']) {
        $html .= '
        <div style="margin-top: 40px; padding: 20px; background: #f8f9fa; border: 1px solid #e9ecef;">
            <h4 style="color: ' . $primaryColor . '; margin-bottom: 15px; font-size: 16px;">Terms and Conditions:</h4>
            <ul style="margin: 0; padding-left: 20px; color: #6c757d; line-height: 1.6;">
                <li>Payment is due within ' . htmlspecialchars($options['payment_terms']) . ' of invoice date.</li>
                <li>Late payments may be subject to a 1.5% monthly service charge.</li>
                <li>All shipments are insured up to $100 unless additional insurance is purchased.</li>
                <li>Claims for damaged or lost items must be reported within 30 days.</li>
                <li>ELTA Courier is not responsible for delays due to weather or customs.</li>
            </ul>
        </div>';
    }

    $html .= '
        </div>
    </div>';

    return $html;
}

function generateMinimalInvoiceHtml($shipment, $options, $invoice_number, $invoice_date, $due_date) {
    // Get appearance settings
    $appearanceSettings = [];
    try {
        Database::prepare("SELECT name, value FROM settings WHERE category = 'appearance'");
        Database::execute();
        $results = Database::fetchAll();

        foreach ($results as $setting) {
            $appearanceSettings[$setting['name']] = $setting['value'];
        }
    } catch (Exception $e) {
        // Use defaults if settings not found
    }

    $primaryColor = $appearanceSettings['primary_color'] ?? '#000000';
    $logoPath = $appearanceSettings['company_logo'] ?? '';

    // Logo HTML
    $logoHtml = '';
    if ($options['include_logo'] && !empty($logoPath) && file_exists('public/uploads/logos/' . $logoPath)) {
        $logoHtml = '<img src="/courier/public/uploads/logos/' . $logoPath . '" alt="Company Logo" style="max-height: 50px; max-width: 150px; object-fit: contain;">';
    }

    $html = '
    <div class="invoice minimal-design" style="width: 100%; max-width: 800px; margin: 0 auto; font-family: \'Helvetica Neue\', Arial, sans-serif; background: white; padding: 40px; color: #333;">
        <!-- Header Section -->
        <div class="invoice-header" style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 40px; padding-bottom: 20px; border-bottom: 2px solid ' . $primaryColor . ';">
            <div class="company-info">
                ' . ($logoHtml ? $logoHtml : '
                <div class="logo" style="font-size: 24px; font-weight: 700; color: ' . $primaryColor . '; margin-bottom: 5px;">ELTA COURIER</div>') . '
                <div style="font-size: 12px; color: #666; margin-top: 5px;">Professional Courier Services</div>
            </div>
            <div class="invoice-info" style="text-align: right;">
                <h1 style="margin: 0; font-size: 36px; font-weight: 100; color: ' . $primaryColor . '; letter-spacing: 2px;">INVOICE</h1>
                <div style="font-size: 14px; margin-top: 5px; color: #666;">' . htmlspecialchars($invoice_number) . '</div>
            </div>
        </div>

        <!-- Invoice & Shipment Info -->
        <div style="display: flex; justify-content: space-between; margin-bottom: 40px;">
            <div style="flex: 1;">
                <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                        <td style="padding: 8px 0; font-weight: 600; color: #333; width: 120px;">Invoice Date:</td>
                        <td style="padding: 8px 0; color: #666;">' . htmlspecialchars($invoice_date) . '</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; font-weight: 600; color: #333;">Due Date:</td>
                        <td style="padding: 8px 0; color: #666;">' . htmlspecialchars($due_date) . '</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; font-weight: 600; color: #333;">Payment Terms:</td>
                        <td style="padding: 8px 0; color: #666;">' . htmlspecialchars($options['payment_terms']) . '</td>
                    </tr>
                </table>
            </div>
            <div style="flex: 1; margin-left: 40px;">
                <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                        <td style="padding: 8px 0; font-weight: 600; color: #333; width: 120px;">Tracking:</td>
                        <td style="padding: 8px 0; color: #666;">' . htmlspecialchars($shipment['tracking_number']) . '</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; font-weight: 600; color: #333;">Service:</td>
                        <td style="padding: 8px 0; color: #666;">' . htmlspecialchars($shipment['service_type'] ?? 'Standard') . '</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; font-weight: 600; color: #333;">Route:</td>
                        <td style="padding: 8px 0; color: #666;">' . htmlspecialchars($shipment['origin']) . ' → ' . htmlspecialchars($shipment['destination']) . '</td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Client Information -->
        <div style="display: flex; justify-content: space-between; margin-bottom: 40px;">
            <div style="flex: 1; margin-right: 40px;">
                <h3 style="margin: 0 0 15px 0; font-size: 14px; font-weight: 600; color: ' . $primaryColor . '; text-transform: uppercase; letter-spacing: 1px;">Bill To</h3>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                        <td style="padding: 8px 0; font-weight: 600; color: #333; border-bottom: 1px solid #eee;">' . htmlspecialchars($shipment['sender_name'] ?? $shipment['shipper_name'] ?? 'N/A') . '</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; color: #666; border-bottom: 1px solid #eee;">' . htmlspecialchars($shipment['sender_address'] ?? $shipment['shipper_address'] ?? 'N/A') . '</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; color: #666; border-bottom: 1px solid #eee;">' . htmlspecialchars($shipment['sender_city'] ?? $shipment['shipper_city'] ?? '') . ', ' . htmlspecialchars($shipment['sender_country'] ?? $shipment['shipper_country'] ?? '') . '</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; color: #666;">' . htmlspecialchars($shipment['sender_phone'] ?? $shipment['shipper_phone'] ?? '') . '</td>
                    </tr>
                </table>
            </div>
            <div style="flex: 1;">
                <h3 style="margin: 0 0 15px 0; font-size: 14px; font-weight: 600; color: ' . $primaryColor . '; text-transform: uppercase; letter-spacing: 1px;">Ship To</h3>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                        <td style="padding: 8px 0; font-weight: 600; color: #333; border-bottom: 1px solid #eee;">' . htmlspecialchars($shipment['receiver_name'] ?? 'N/A') . '</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; color: #666; border-bottom: 1px solid #eee;">' . htmlspecialchars($shipment['receiver_address'] ?? 'N/A') . '</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; color: #666; border-bottom: 1px solid #eee;">' . htmlspecialchars($shipment['receiver_city'] ?? '') . ', ' . htmlspecialchars($shipment['receiver_country'] ?? '') . '</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; color: #666;">' . htmlspecialchars($shipment['receiver_phone'] ?? '') . '</td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Charges Table -->
        <table style="width: 100%; border-collapse: collapse; margin-bottom: 30px;">
            <thead>
                <tr>
                    <th style="padding: 15px 0; text-align: left; font-weight: 600; color: ' . $primaryColor . '; border-bottom: 2px solid ' . $primaryColor . '; font-size: 14px; text-transform: uppercase; letter-spacing: 1px;">Description</th>
                    <th style="padding: 15px 0; text-align: right; font-weight: 600; color: ' . $primaryColor . '; border-bottom: 2px solid ' . $primaryColor . '; font-size: 14px; text-transform: uppercase; letter-spacing: 1px;">Amount</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td style="padding: 12px 0; border-bottom: 1px solid #eee; color: #333;">Shipping Cost</td>
                    <td style="padding: 12px 0; text-align: right; border-bottom: 1px solid #eee; color: #333;">' . $options['currency_symbol'] . number_format($options['shipping_cost'], 2) . '</td>
                </tr>';

    // Add additional charges
    foreach ($options['additional_charges'] as $charge) {
        $html .= '
                <tr>
                    <td style="padding: 12px 0; border-bottom: 1px solid #eee; color: #333;">' . htmlspecialchars($charge['description']) . '</td>
                    <td style="padding: 12px 0; text-align: right; border-bottom: 1px solid #eee; color: #333;">' . $options['currency_symbol'] . number_format($charge['amount'], 2) . '</td>
                </tr>';
    }

    $html .= '
                <tr>
                    <td style="padding: 12px 0; border-bottom: 1px solid #eee; color: #333;">Subtotal</td>
                    <td style="padding: 12px 0; text-align: right; border-bottom: 1px solid #eee; color: #333;">' . $options['currency_symbol'] . number_format($options['subtotal'], 2) . '</td>
                </tr>
                <tr>
                    <td style="padding: 12px 0; border-bottom: 1px solid #eee; color: #333;">Tax (' . $options['tax_rate'] . '%)</td>
                    <td style="padding: 12px 0; text-align: right; border-bottom: 1px solid #eee; color: #333;">' . $options['currency_symbol'] . number_format($options['tax_amount'], 2) . '</td>
                </tr>
                <tr>
                    <td style="padding: 20px 0 15px 0; font-size: 18px; font-weight: 700; color: ' . $primaryColor . '; border-bottom: 2px solid ' . $primaryColor . ';">TOTAL</td>
                    <td style="padding: 20px 0 15px 0; text-align: right; font-size: 18px; font-weight: 700; color: ' . $primaryColor . '; border-bottom: 2px solid ' . $primaryColor . ';">' . $options['currency_symbol'] . number_format($options['total_amount'], 2) . '</td>
                </tr>
            </tbody>
        </table>';

    // Add notes if provided
    if (!empty($options['notes'])) {
        $html .= '
        <div style="margin-bottom: 30px; padding: 20px; background: #f9f9f9; border: 1px solid #e9ecef;">
            <h4 style="margin: 0 0 10px 0; font-size: 14px; font-weight: 600; color: ' . $primaryColor . '; text-transform: uppercase; letter-spacing: 1px;">Notes</h4>
            <p style="margin: 0; color: #666; line-height: 1.5;">' . nl2br(htmlspecialchars($options['notes'])) . '</p>
        </div>';
    }

    // Add terms if enabled
    if ($options['include_terms']) {
        $html .= '
        <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee;">
            <h4 style="margin: 0 0 15px 0; font-size: 14px; font-weight: 600; color: ' . $primaryColor . '; text-transform: uppercase; letter-spacing: 1px;">Terms and Conditions</h4>
            <div style="font-size: 12px; color: #666; line-height: 1.6;">
                <p style="margin: 0 0 8px 0;">• Payment is due within ' . htmlspecialchars($options['payment_terms']) . ' of invoice date.</p>
                <p style="margin: 0 0 8px 0;">• Late payments may be subject to a 1.5% monthly service charge.</p>
                <p style="margin: 0 0 8px 0;">• All shipments are insured up to $100 unless additional insurance is purchased.</p>
                <p style="margin: 0 0 8px 0;">• Claims for damaged or lost items must be reported within 30 days.</p>
                <p style="margin: 0;">• ELTA Courier is not responsible for delays due to weather or customs.</p>
            </div>
        </div>';
    }

    $html .= '
    </div>';

    return $html;
}

function generateInvoicePdf($html, $shipment) {
    // For now, return a simple PDF placeholder
    // In a real implementation, you would use a library like TCPDF or DomPDF
    $invoice_number = 'INV-' . date('Y') . '-' . str_pad($shipment['id'], 6, '0', STR_PAD_LEFT);
    
    $pdf_content = "%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 60
>>
stream
BT
/F1 12 Tf
72 720 Td
(Invoice " . $invoice_number . " - " . $shipment['tracking_number'] . ") Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
320
%%EOF";
    
    return $pdf_content;
}
?>
