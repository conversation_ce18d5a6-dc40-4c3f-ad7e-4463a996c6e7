<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tracking Details - <?= App\Core\Config::get('app.name', 'Courier Service') ?></title>
    <link rel="stylesheet" href="<?= App\Core\View::asset('css/public-style.css') ?>">
    <link rel="stylesheet" href="<?= App\Core\View::asset('css/tracking-details.css') ?>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
</head>
<body class="tracking-page-body">
    <div class="tracking-container tracking-result-container">
        <div class="tracking-header">
            <a href="<?= App\Core\View::url('/track') ?>" class="back-link">
                <i class="fas fa-arrow-left"></i> Back to Tracking
            </a>
            <h1 class="tracking-title">Shipment Details</h1>
        </div>

        <div class="shipment-details-card">
            <div class="card-header">
                <div class="card-title">Tracking Number: <?= $shipment['tracking_number'] ?? 'Unknown' ?></div>
                <div class="status-badge status-<?= strtolower(str_replace(' ', '-', $shipment['status'] ?? 'pending')) ?>">
                    <i class="fas fa-circle"></i> <?= ucwords(str_replace('_', ' ', $shipment['status'] ?? 'Pending')) ?>
                </div>
            </div>
            <div class="card-body">
                <div class="details-grid">
                    <div class="detail-item">
                        <div class="detail-label">SHIPPER</div>
                        <div class="detail-value"><?= $shipment['shipper_name'] ?? 'N/A' ?></div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">RECEIVER</div>
                        <div class="detail-value"><?= $shipment['receiver_name'] ?? 'N/A' ?></div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">ORIGIN</div>
                        <div class="detail-value"><?= $shipment['origin'] ?? 'N/A' ?></div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">DESTINATION</div>
                        <div class="detail-value"><?= $shipment['destination'] ?? 'N/A' ?></div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">PICKUP DATE</div>
                        <div class="detail-value"><?= $shipment['pickup_date'] ?? 'N/A' ?></div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">ESTIMATED DELIVERY</div>
                        <div class="detail-value"><?= $shipment['estimated_delivery'] ?? 'N/A' ?></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Shipment Status Chart -->
        <div class="shipment-status-section">
            <div class="section-title">Shipment Status</div>
            <div id="shipment-status-chart"></div>
        </div>

        <!-- Delivery Route Map -->
        <div class="delivery-map-section">
            <div class="section-title">Delivery Route</div>
            <div id="delivery-route-map"></div>
        </div>

        <!-- Shipment Timeline -->
        <div class="shipment-timeline">
            <div class="timeline-track"></div>
            <div class="section-title">Shipment Progress</div>
            
            <?php if (!empty($shipment['events'])): ?>
                <?php foreach ($shipment['events'] as $index => $event): ?>
                    <div class="timeline-event">
                        <div class="event-dot <?= $index === 0 ? 'active' : '' ?>"></div>
                        <div class="event-content">
                            <div class="event-date"><?= $event['date'] ?> <?= $event['time'] ?></div>
                            <div class="event-title"><?= $event['status'] ?></div>
                            <div class="event-location"><i class="fas fa-map-marker-alt"></i> <?= $event['location'] ?></div>
                            <?php if (!empty($event['message'])): ?>
                                <div class="event-message"><?= $event['message'] ?></div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="no-events">No tracking events available yet.</div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
    <script src="<?= App\Core\View::asset('js/tracking-details.js') ?>"></script>
</body>
</html>
