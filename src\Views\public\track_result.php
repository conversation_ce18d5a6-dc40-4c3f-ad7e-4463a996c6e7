<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= App\Core\View::e($page_title ?? 'Tracking Details') ?> - <?= App\Core\Config::get('app.name', 'Courier Service') ?></title>
    <link rel="stylesheet" href="<?= App\Core\View::asset('css/public-style.css') ?>">
    <link rel="stylesheet" href="<?= App\Core\View::asset('css/track-result.css') ?>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
</head>
<body class="tracking-page-body">
    <div class="tracking-container tracking-result-container">
        <div class="tracking-layout">
            <!-- New Icon Sidebar -->
            <div class="new-tracking-sidebar">
                <!-- Brand/Logo -->
                <div class="sidebar-brand">
                    <a href="<?= App\Core\View::url('/') ?>">
                        <i class="fas fa-shipping-fast"></i>
                    </a>
                </div>

                <!-- Main Icons -->
                <div class="sidebar-icons">
                    <div class="sidebar-icon active" data-section="tracking" title="Track Shipment">
                        <i class="fas fa-search"></i>
                        <div class="sidebar-tooltip">Track Shipment</div>
                    </div>

                    <div class="sidebar-icon" data-section="document-history" title="Documents">
                        <i class="fas fa-file-alt"></i>
                        <span class="notification-badge" id="document-count-badge" style="display: none;">0</span>
                        <div class="sidebar-tooltip">Documents</div>
                    </div>

                    <div class="sidebar-icon" data-section="notifications" title="Notifications">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge" id="notification-count-badge" style="display: none;">0</span>
                        <div class="sidebar-tooltip">Notifications</div>
                    </div>

                    <div class="sidebar-icon" data-section="track-details" title="Shipment Details">
                        <i class="fas fa-info-circle"></i>
                        <div class="sidebar-tooltip">Shipment Details</div>
                    </div>

                    <div class="sidebar-icon" data-section="map" title="Track on Map">
                        <i class="fas fa-map-marker-alt"></i>
                        <div class="sidebar-tooltip">Track on Map</div>
                    </div>

                    <div class="sidebar-icon" data-section="history" title="Tracking History">
                        <i class="fas fa-history"></i>
                        <div class="sidebar-tooltip">Tracking History</div>
                    </div>
                </div>

                <!-- Bottom Icons -->
                <div class="sidebar-bottom">
                    <div class="sidebar-icon" data-section="help" title="Help & Support">
                        <i class="fas fa-question-circle"></i>
                        <div class="sidebar-tooltip">Help & Support</div>
                    </div>

                    <div class="sidebar-icon" onclick="window.location.href='<?= App\Core\View::url('/') ?>'" title="Back to Home">
                        <i class="fas fa-home"></i>
                        <div class="sidebar-tooltip">Back to Home</div>
                    </div>
                </div>
            </div>
                    <a href="<?= App\Core\View::url('/') ?>" class="nav-item" title="Services">
                        <i class="fas fa-box"></i>
                    </a>
                    <a href="<?= App\Core\View::url('/') ?>" class="nav-item" title="Contact">
                        <i class="fas fa-envelope"></i>
                    </a>
                </div>
            </div>

            <!-- Main Content -->
            <div class="tracking-content">
                <div class="tracking-header">
                    <h1 class="tracking-title">Tracking List</h1>
                    <p class="tracking-subtitle">Track your shipment</p>

                    <!-- Search Form -->
                    <div class="tracking-search">
                        <form action="<?= App\Core\View::url('/track') ?>" method="GET">
                            <div class="search-input-group">
                                <i class="fas fa-search search-icon"></i>
                                <input type="text" name="tracking_number" placeholder="Enter tracking number..." class="search-input">
                            </div>
                        </form>
                    </div>
                </div>

                <div class="tracking-main">
                    <!-- Order ID and Status -->
                    <div class="order-header">
                        <div class="order-id">
                            <span class="label">Order ID:</span>
                            <span class="value"><?= App\Core\View::e($shipment['tracking_number']) ?></span>
                        </div>
                        <?php
                            $statusClass = strtolower(str_replace(' ', '-', $shipment['status']));
                        ?>
                        <div class="order-status <?= $statusClass ?>">
                            <?= App\Core\View::e(ucwords(str_replace('_', ' ', $shipment['status']))) ?>
                        </div>
                    </div>

                    <!-- Customer Info Cards -->
                    <div class="info-cards">
                        <div class="info-card">
                            <div class="info-card-header">
                                <i class="fas fa-user"></i>
                                <span>Customer</span>
                                <button class="more-btn"><i class="fas fa-ellipsis-v"></i></button>
                            </div>
                            <div class="info-card-content">
                                <?php if ($client): ?>
                                    <?= App\Core\View::e($client['name']) ?>
                                <?php else: ?>
                                    <?= App\Core\View::e($shipment['shipper_name']) ?>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="info-card">
                            <div class="info-card-header">
                                <i class="fas fa-phone"></i>
                                <span>Phone Number</span>
                                <button class="more-btn"><i class="fas fa-ellipsis-v"></i></button>
                            </div>
                            <div class="info-card-content">
                                <?php if ($client && !empty($client['phone'])): ?>
                                    <?= App\Core\View::e($client['phone']) ?>
                                <?php elseif (!empty($shipment['shipper_phone'])): ?>
                                    <?= App\Core\View::e($shipment['shipper_phone']) ?>
                                <?php else: ?>
                                    N/A
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="info-card">
                            <div class="info-card-header">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>Address</span>
                                <button class="more-btn"><i class="fas fa-ellipsis-v"></i></button>
                            </div>
                            <div class="info-card-content">
                                <?= App\Core\View::e($shipment['destination']) ?>
                            </div>
                        </div>
                    </div>

                    <!-- Map and Shipment Details -->
                    <div class="tracking-details">
                        <!-- Map -->
                        <div class="map-container">
                            <div id="shipment-map"></div>
                        </div>

                        <!-- Tabs -->
                        <div class="tabs-container">
                            <div class="tabs-header">
                                <button class="tab-btn active" data-tab="detail-status">Detail Status</button>
                                <button class="tab-btn" data-tab="vehicle">Vehicle</button>
                                <button class="tab-btn" data-tab="driver-info">Driver Information</button>
                                <button class="tab-btn" data-tab="item-list">Item List</button>
                                <button class="tab-btn" data-tab="customer-info">Customer Information</button>
                            </div>

                            <div class="tab-content">
                                <!-- Detail Status Tab -->
                                <div class="tab-pane active" id="detail-status">
                                    <div class="shipment-timeline">
                                        <?php if (!empty($history)): ?>
                                            <?php foreach ($history as $event): ?>
                                                <div class="timeline-event">
                                                    <div class="event-dot <?= $event['status'] === $shipment['status'] ? 'active' : '' ?>"></div>
                                                    <div class="event-content">
                                                        <div class="event-date">
                                                            <?= App\Core\View::e(date('F j, Y g:i A', strtotime($event['date_time']))) ?>
                                                        </div>
                                                        <div class="event-title">
                                                            <?= App\Core\View::e(ucwords(str_replace('_', ' ', $event['status']))) ?>
                                                        </div>
                                                        <div class="event-location">
                                                            <?= App\Core\View::e($event['location']) ?>
                                                        </div>
                                                        <?php if (!empty($event['message'])): ?>
                                                            <div class="event-message">
                                                                <?= App\Core\View::e($event['message']) ?>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <p class="no-events">No tracking events available yet.</p>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <!-- Vehicle Tab -->
                                <div class="tab-pane" id="vehicle">
                                    <div class="placeholder-content">
                                        <i class="fas fa-truck-moving placeholder-icon"></i>
                                        <p>Vehicle information not available</p>
                                    </div>
                                </div>

                                <!-- Driver Information Tab -->
                                <div class="tab-pane" id="driver-info">
                                    <?php if ($employee): ?>
                                        <div class="driver-info">
                                            <div class="driver-header">
                                                <div class="driver-avatar">
                                                    <i class="fas fa-user-circle"></i>
                                                </div>
                                                <div class="driver-name">
                                                    <?= App\Core\View::e($employee['name']) ?>
                                                </div>
                                            </div>
                                            <div class="driver-details">
                                                <?php if (!empty($employee['phone'])): ?>
                                                    <div class="driver-detail">
                                                        <span class="label">Phone:</span>
                                                        <span class="value"><?= App\Core\View::e($employee['phone']) ?></span>
                                                    </div>
                                                <?php endif; ?>
                                                <?php if (!empty($employee['email'])): ?>
                                                    <div class="driver-detail">
                                                        <span class="label">Email:</span>
                                                        <span class="value"><?= App\Core\View::e($employee['email']) ?></span>
                                                    </div>
                                                <?php endif; ?>
                                                <?php if (!empty($employee['position'])): ?>
                                                    <div class="driver-detail">
                                                        <span class="label">Position:</span>
                                                        <span class="value"><?= App\Core\View::e($employee['position']) ?></span>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php else: ?>
                                        <div class="placeholder-content">
                                            <i class="fas fa-user-circle placeholder-icon"></i>
                                            <p>Driver information not available</p>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <!-- Item List Tab -->
                                <div class="tab-pane" id="item-list">
                                    <div class="item-list-header">
                                        <h3>Item List</h3>
                                        <div class="item-list-actions">
                                            <button class="action-btn"><i class="fas fa-print"></i></button>
                                            <button class="action-btn"><i class="fas fa-download"></i></button>
                                        </div>
                                    </div>

                                    <?php if (!empty($packages)): ?>
                                        <div class="item-list-table">
                                            <table>
                                                <thead>
                                                    <tr>
                                                        <th>#</th>
                                                        <th>Items Name</th>
                                                        <th>Category</th>
                                                        <th>Weight</th>
                                                        <th>Quantity</th>
                                                        <th>Action</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($packages as $index => $package): ?>
                                                        <tr>
                                                            <td><?= $index + 1 ?></td>
                                                            <td>
                                                                <?php if (!empty($package['description'])): ?>
                                                                    <?= App\Core\View::e($package['description']) ?>
                                                                <?php else: ?>
                                                                    <?= App\Core\View::e($package['piece_type'] ?? 'Package') ?>
                                                                <?php endif; ?>
                                                            </td>
                                                            <td><?= App\Core\View::e($package['piece_type'] ?? 'Standard') ?></td>
                                                            <td><?= App\Core\View::e($package['weight'] ?? 'N/A') ?> kg</td>
                                                            <td>x<?= App\Core\View::e($package['quantity'] ?? 1) ?></td>
                                                            <td>
                                                                <button class="item-action-btn"><i class="fas fa-ellipsis-v"></i></button>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    <?php else: ?>
                                        <div class="placeholder-content">
                                            <i class="fas fa-box-open placeholder-icon"></i>
                                            <p>No items available for this shipment</p>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <!-- Customer Information Tab -->
                                <div class="tab-pane" id="customer-info">
                                    <div class="customer-info">
                                        <div class="customer-section">
                                            <h3 class="section-title">Shipper Information</h3>
                                            <div class="customer-details">
                                                <div class="customer-detail">
                                                    <span class="label">Name:</span>
                                                    <span class="value"><?= App\Core\View::e($shipment['shipper_name']) ?></span>
                                                </div>
                                                <div class="customer-detail">
                                                    <span class="label">Address:</span>
                                                    <span class="value"><?= App\Core\View::e($shipment['shipper_address']) ?></span>
                                                </div>
                                                <?php if (!empty($shipment['shipper_phone'])): ?>
                                                    <div class="customer-detail">
                                                        <span class="label">Phone:</span>
                                                        <span class="value"><?= App\Core\View::e($shipment['shipper_phone']) ?></span>
                                                    </div>
                                                <?php endif; ?>
                                                <?php if (!empty($shipment['shipper_email'])): ?>
                                                    <div class="customer-detail">
                                                        <span class="label">Email:</span>
                                                        <span class="value"><?= App\Core\View::e($shipment['shipper_email']) ?></span>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                        <div class="customer-section">
                                            <h3 class="section-title">Receiver Information</h3>
                                            <div class="customer-details">
                                                <div class="customer-detail">
                                                    <span class="label">Name:</span>
                                                    <span class="value"><?= App\Core\View::e($shipment['receiver_name']) ?></span>
                                                </div>
                                                <div class="customer-detail">
                                                    <span class="label">Address:</span>
                                                    <span class="value"><?= App\Core\View::e($shipment['receiver_address']) ?></span>
                                                </div>
                                                <?php if (!empty($shipment['receiver_phone'])): ?>
                                                    <div class="customer-detail">
                                                        <span class="label">Phone:</span>
                                                        <span class="value"><?= App\Core\View::e($shipment['receiver_phone']) ?></span>
                                                    </div>
                                                <?php endif; ?>
                                                <?php if (!empty($shipment['receiver_email'])): ?>
                                                    <div class="customer-detail">
                                                        <span class="label">Email:</span>
                                                        <span class="value"><?= App\Core\View::e($shipment['receiver_email']) ?></span>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                        <div class="customer-section">
                                            <h3 class="section-title">Shipment Details</h3>
                                            <div class="customer-details">
                                                <div class="customer-detail">
                                                    <span class="label">Origin:</span>
                                                    <span class="value"><?= App\Core\View::e($shipment['origin']) ?></span>
                                                </div>
                                                <div class="customer-detail">
                                                    <span class="label">Destination:</span>
                                                    <span class="value"><?= App\Core\View::e($shipment['destination']) ?></span>
                                                </div>
                                                <?php if (!empty($shipment['shipment_type'])): ?>
                                                    <div class="customer-detail">
                                                        <span class="label">Shipment Type:</span>
                                                        <span class="value"><?= App\Core\View::e($shipment['shipment_type']) ?></span>
                                                    </div>
                                                <?php endif; ?>
                                                <?php if (!empty($shipment['shipment_mode'])): ?>
                                                    <div class="customer-detail">
                                                        <span class="label">Shipment Mode:</span>
                                                        <span class="value"><?= App\Core\View::e($shipment['shipment_mode']) ?></span>
                                                    </div>
                                                <?php endif; ?>
                                                <?php if (!empty($shipment['total_weight'])): ?>
                                                    <div class="customer-detail">
                                                        <span class="label">Weight:</span>
                                                        <span class="value"><?= App\Core\View::e($shipment['total_weight']) ?> kg</span>
                                                    </div>
                                                <?php endif; ?>
                                                <?php if (!empty($shipment['pickup_date_formatted'])): ?>
                                                    <div class="customer-detail">
                                                        <span class="label">Pickup Date:</span>
                                                        <span class="value"><?= App\Core\View::e($shipment['pickup_date_formatted']) ?></span>
                                                    </div>
                                                <?php endif; ?>
                                                <?php if (!empty($shipment['expected_delivery_date_formatted'])): ?>
                                                    <div class="customer-detail">
                                                        <span class="label">Expected Delivery:</span>
                                                        <span class="value"><?= App\Core\View::e($shipment['expected_delivery_date_formatted']) ?></span>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Tab functionality
            const tabBtns = document.querySelectorAll('.tab-btn');
            const tabPanes = document.querySelectorAll('.tab-pane');

            tabBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // Remove active class from all buttons and panes
                    tabBtns.forEach(b => b.classList.remove('active'));
                    tabPanes.forEach(p => p.classList.remove('active'));

                    // Add active class to clicked button and corresponding pane
                    this.classList.add('active');
                    const tabId = this.getAttribute('data-tab');
                    document.getElementById(tabId).classList.add('active');
                });
            });

            // Initialize map
            const map = L.map('shipment-map').setView([0, 0], 2);

            // Use a grayscale tile layer for black and white theme
            L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
                subdomains: 'abcd',
                maxZoom: 19
            }).addTo(map);

            // Placeholder for origin and destination coordinates
            // In a real implementation, these would come from geocoding the addresses
            const origin = [<?= $shipment['origin_lat'] ?? '-7.2575' ?>, <?= $shipment['origin_lng'] ?? '112.7521' ?>]; // Default: Surabaya
            const destination = [<?= $shipment['destination_lat'] ?? '-7.9797' ?>, <?= $shipment['destination_lng'] ?? '112.6304' ?>]; // Default: Malang

            // Add markers for origin and destination
            const originMarker = L.marker(origin).addTo(map)
                .bindPopup('Origin: <?= addslashes($shipment['origin']) ?>');

            const destinationMarker = L.marker(destination).addTo(map)
                .bindPopup('Destination: <?= addslashes($shipment['destination']) ?>');

            // Draw a line between origin and destination
            const route = L.polyline([origin, destination], {
                color: '#3388ff',
                weight: 3,
                opacity: 0.7
            }).addTo(map);

            // Fit the map to show both markers
            const bounds = L.latLngBounds([origin, destination]);
            map.fitBounds(bounds, { padding: [50, 50] });
        });
    </script>
</body>
</html>
