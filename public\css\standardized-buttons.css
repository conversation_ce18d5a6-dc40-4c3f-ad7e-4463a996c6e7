/* Standardized Button System - Uses Appearance Settings */

/* Base button styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    margin: 0.125rem;
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    border: 1px solid transparent;
    border-radius: 0.375rem;
    transition: all 0.15s ease-in-out;
    white-space: nowrap;
}

.btn:hover {
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn:focus {
    outline: 0;
    box-shadow: 0 0 0 3px rgba(var(--secondary-rgb, 160, 70, 70), 0.25);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn i {
    margin-right: 0.5rem;
    font-size: 0.875rem;
}

.btn i:last-child {
    margin-right: 0;
    margin-left: 0.5rem;
}

.btn i:only-child {
    margin: 0;
}

/* Primary Button - Uses Secondary Color from Appearance Settings */
.btn-primary {
    color: #ffffff;
    background-color: var(--secondary-color, #a04646);
    border-color: var(--secondary-color, #a04646);
}

.btn-primary:hover {
    color: #ffffff;
    background-color: rgba(var(--secondary-rgb, 160, 70, 70), 0.9);
    border-color: rgba(var(--secondary-rgb, 160, 70, 70), 0.9);
}

/* Secondary Button - Uses Primary Color from Appearance Settings */
.btn-secondary {
    color: #ffffff;
    background-color: var(--primary-color, #1a1a1a);
    border-color: var(--primary-color, #1a1a1a);
}

.btn-secondary:hover {
    color: #ffffff;
    background-color: rgba(var(--primary-rgb, 26, 26, 26), 0.9);
    border-color: rgba(var(--primary-rgb, 26, 26, 26), 0.9);
}

/* Success Button */
.btn-success {
    color: #ffffff;
    background-color: #28a745;
    border-color: #28a745;
}

.btn-success:hover {
    color: #ffffff;
    background-color: #218838;
    border-color: #1e7e34;
}

/* Danger Button */
.btn-danger {
    color: #ffffff;
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-danger:hover {
    color: #ffffff;
    background-color: #c82333;
    border-color: #bd2130;
}

/* Warning Button */
.btn-warning {
    color: #212529;
    background-color: #ffc107;
    border-color: #ffc107;
}

.btn-warning:hover {
    color: #212529;
    background-color: #e0a800;
    border-color: #d39e00;
}

/* Info Button */
.btn-info {
    color: #ffffff;
    background-color: #17a2b8;
    border-color: #17a2b8;
}

.btn-info:hover {
    color: #ffffff;
    background-color: #138496;
    border-color: #117a8b;
}

/* Light Button */
.btn-light {
    color: #212529;
    background-color: #f8f9fa;
    border-color: #f8f9fa;
}

.btn-light:hover {
    color: #212529;
    background-color: #e2e6ea;
    border-color: #dae0e5;
}

/* Dark Button */
.btn-dark {
    color: #ffffff;
    background-color: #343a40;
    border-color: #343a40;
}

.btn-dark:hover {
    color: #ffffff;
    background-color: #23272b;
    border-color: #1d2124;
}

/* Outline Buttons */
.btn-outline-primary {
    color: var(--secondary-color, #a04646);
    border-color: var(--secondary-color, #a04646);
    background-color: #fff;
}

.btn-outline-primary:hover {
    color: #ffffff;
    background-color: var(--secondary-color, #a04646);
    border-color: var(--secondary-color, #a04646);
}

.btn-outline-secondary {
    color: var(--primary-color, #1a1a1a);
    border-color: var(--primary-color, #1a1a1a);
    background-color: #f8f9fa;
}

.btn-outline-secondary:hover {
    color: #ffffff;
    background-color: var(--primary-color, #1a1a1a);
    border-color: var(--primary-color, #1a1a1a);
}

/* Button Sizes */
.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 0.25rem;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    border-radius: 0.5rem;
}

/* Button Groups */
.btn-group {
    display: inline-flex;
    vertical-align: middle;
}

.btn-group .btn {
    margin: 0;
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.btn-group .btn:last-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

.btn-group .btn:not(:first-child) {
    border-left: 0;
}

/* Action Button Specific Styles */
.action-btn {
    width: 36px;
    height: 36px;
    padding: 0;
    border-radius: 0.375rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.action-btn i {
    margin: 0;
    font-size: 1rem;
}

/* Table Action Buttons */
.table-actions {
    display: flex;
    gap: 0.25rem;
    align-items: center;
}

.table-actions .btn {
    padding: 0.375rem;
    min-width: 32px;
    height: 32px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.8rem;
    }
    
    .btn-lg {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
    
    .btn-sm {
        padding: 0.2rem 0.4rem;
        font-size: 0.7rem;
    }
}
