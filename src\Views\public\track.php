<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Track Your Shipment - <?= App\Core\Config::get('app.name', 'Courier Service') ?></title>
    <link rel="stylesheet" href="<?= App\Core\View::asset('css/public-style.css') ?>">
    <link rel="stylesheet" href="<?= App\Core\View::asset('css/track-result.css') ?>">
    <link rel="stylesheet" href="<?= App\Core\View::asset('css/tracking-details.css') ?>">
    <link rel="stylesheet" href="<?= App\Core\View::asset('css/custom-theme.css') ?>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
<style>
/* Ensure notification modal overlays and is visible */
.notification-modal {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.4);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.notification-modal-content {
  background: #fff;
  border-radius: 8px;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 4px 24px rgba(0,0,0,0.18);
  padding: 24px 20px 16px 20px;
  position: relative;
}
.notification-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
.notification-modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
}
.notification-modal-footer {
  text-align: right;
  margin-top: 16px;
}
.notification-modal-time {
  color: #888;
  font-size: 0.95em;
  margin-bottom: 8px;
}
</style>
</head>
<body class="tracking-page-body">
    <div class="tracking-container tracking-result-container">
        <div class="tracking-layout">
            <!-- New Icon Sidebar -->
            <div class="new-tracking-sidebar">
                <!-- Brand/Logo -->
                <div class="sidebar-brand">
                    <a href="<?= App\Core\View::url('/') ?>">
                        <i class="fas fa-shipping-fast"></i>
                    </a>
                </div>

                <!-- Main Icons -->
                <div class="sidebar-icons">
                    <div class="sidebar-icon active" data-section="tracking" title="Track Shipment">
                        <i class="fas fa-search"></i>
                        <div class="sidebar-tooltip">Track Shipment</div>
                    </div>

                    <div class="sidebar-icon" data-section="document-history" title="Documents">
                        <i class="fas fa-file-alt"></i>
                        <span class="notification-badge" id="document-count-badge" style="display: none;">0</span>
                        <div class="sidebar-tooltip">Documents</div>
                    </div>

                    <div class="sidebar-icon" data-section="notifications" title="Notifications">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge" id="notification-count-badge" style="display: none;">0</span>
                        <div class="sidebar-tooltip">Notifications</div>
                    </div>

                    <div class="sidebar-icon" data-section="track-details" title="Shipment Details">
                        <i class="fas fa-info-circle"></i>
                        <div class="sidebar-tooltip">Shipment Details</div>
                    </div>

                    <div class="sidebar-icon" data-section="map" title="Track on Map">
                        <i class="fas fa-map-marker-alt"></i>
                        <div class="sidebar-tooltip">Track on Map</div>
                    </div>

                    <div class="sidebar-icon" data-section="history" title="Tracking History">
                        <i class="fas fa-history"></i>
                        <div class="sidebar-tooltip">Tracking History</div>
                    </div>
                </div>

                <!-- Bottom Icons -->
                <div class="sidebar-bottom">
                    <div class="sidebar-icon" data-section="help" title="Help & Support">
                        <i class="fas fa-question-circle"></i>
                        <div class="sidebar-tooltip">Help & Support</div>
                    </div>

                    <div class="sidebar-icon" onclick="window.location.href='<?= App\Core\View::url('/') ?>'" title="Back to Home">
                        <i class="fas fa-home"></i>
                        <div class="sidebar-tooltip">Back to Home</div>
                    </div>
                </div>
            </div>

            <!-- Sliding Panels -->
            <div class="sidebar-panel" id="document-history-panel">
                <div class="panel-header">
                    <h3>Document History</h3>
                    <button class="panel-close" onclick="closeSidebarPanel()">&times;</button>
                </div>
                <div class="panel-content" id="document-history-content">
                    <div class="no-content">
                        <i class="fas fa-file-alt"></i>
                        <p>No document history available</p>
                    </div>
                </div>
            </div>

            <div class="sidebar-panel" id="notifications-panel">
                <div class="panel-header">
                    <h3>Notifications</h3>
                    <button class="panel-close" onclick="closeSidebarPanel()">&times;</button>
                </div>
                <div class="panel-content" id="notifications-content">
                    <div class="no-content">
                        <i class="fas fa-bell"></i>
                        <p>No notifications available</p>
                    </div>
                </div>
            </div>

            <div class="sidebar-panel" id="track-details-panel">
                <div class="panel-header">
                    <h3>Track Details</h3>
                    <button class="panel-close" onclick="closeSidebarPanel()">&times;</button>
                </div>
                <div class="panel-content" id="track-details-content">
                    <div class="no-content">
                        <i class="fas fa-info-circle"></i>
                        <p>No tracking information available</p>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="tracking-content">
                <div class="tracking-header">
                    <h1 class="tracking-title">Tracking List</h1>
                    <p class="tracking-subtitle">Track your shipment</p>
                </div>

                <div class="tracking-main">
                    <div class="tracking-search-container">
                        <form id="tracking-form" class="tracking-form" action="<?= App\Core\View::url('/api/track') ?>" method="POST">
                            <?= App\Core\View::csrfField() ?>
                            <div class="search-input-group">
                                <i class="fas fa-search search-icon"></i>
                                <input type="text" id="tracking_number" name="tracking_number" class="search-input" placeholder="Enter tracking number..." required>
                                <button type="submit" class="search-button">
                                    <i class="fas fa-search"></i> Track
                                </button>
                            </div>
                            <p class="tracking-note">
                                <i class="fas fa-info-circle note-icon"></i>
                                <span>Please Note: Shipment information will appear approximately 8 hours after being handed in at a <?= App\Models\Setting::get('site_name', 'Courier Service') ?> location</span>
                            </p>
                        </form>
                    </div>

                    <div id="tracking-results" class="tracking-results-area">
                        <!-- Results will be loaded here via JS -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <script src="<?= App\Core\View::asset('js/public-script.js') ?>"></script>
    <script src="<?= App\Core\View::asset('js/tracking-details.js') ?>"></script>
</body>
</html>
