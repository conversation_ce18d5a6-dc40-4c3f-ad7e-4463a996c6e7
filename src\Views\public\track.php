<?php
use App\Core\View;
use App\Core\Session;
use App\Models\Setting;

// Load appearance and general settings
$appearanceSettings = Setting::getByGroup('appearance');
$generalSettings = Setting::getByGroup('general');

// Extract settings with defaults
$sidebarBgColor = $appearanceSettings['sidebar_bg_color'] ?? '#ffffff';
$sidebarTextColor = $appearanceSettings['sidebar_text_color'] ?? '#333333';
$sidebarHoverColor = $appearanceSettings['sidebar_hover_color'] ?? '#f8f9fa';
$primaryColor = $appearanceSettings['primary_color'] ?? '#2c3e50';
$logoPath = $appearanceSettings['company_logo'] ?? '';
$appName = $generalSettings['app_name'] ?? 'ELTA Courier';
$favicon = $generalSettings['favicon'] ?? '';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Track Your Shipment - <?= View::e($appName) ?></title>
    <link rel="stylesheet" href="<?= App\Core\View::asset('css/public-style.css') ?>">
    <link rel="stylesheet" href="<?= App\Core\View::asset('css/track-result.css') ?>">
    <link rel="stylesheet" href="<?= App\Core\View::asset('css/tracking-details.css') ?>">
    <link rel="stylesheet" href="<?= App\Core\View::asset('css/custom-theme.css') ?>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
<style>
/* Dynamic Sidebar Theming */
.new-tracking-sidebar {
    background: <?= $sidebarBgColor ?> !important;
    <?php if ($sidebarBgColor === '#ffffff'): ?>
    border-right: 1px solid #e9ecef;
    <?php endif; ?>
}

.sidebar-icon {
    color: <?= $sidebarTextColor ?> !important;
    background: rgba(<?= $sidebarBgColor === '#ffffff' ? '0,0,0,0.05' : '255,255,255,0.1' ?>) !important;
    border: 1px solid rgba(<?= $sidebarBgColor === '#ffffff' ? '0,0,0,0.1' : '255,255,255,0.1' ?>) !important;
}

.sidebar-icon:hover {
    background: <?= $sidebarHoverColor ?> !important;
    color: <?= $primaryColor ?> !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(<?= hexdec(substr($primaryColor, 1, 2)) ?>, <?= hexdec(substr($primaryColor, 3, 2)) ?>, <?= hexdec(substr($primaryColor, 5, 2)) ?>, 0.3);
}

.sidebar-icon.active {
    background: <?= $primaryColor ?> !important;
    color: #ffffff !important;
    box-shadow: 0 4px 15px rgba(<?= hexdec(substr($primaryColor, 1, 2)) ?>, <?= hexdec(substr($primaryColor, 3, 2)) ?>, <?= hexdec(substr($primaryColor, 5, 2)) ?>, 0.4);
}

.sidebar-brand i {
    color: #ffffff !important;
    background: <?= $primaryColor ?> !important;
}

/* Notification Modal Styles */
.notification-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    display: none;
    align-items: center;
    justify-content: center;
}

.notification-modal.active {
    display: flex;
}

.notification-modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.notification-modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    border-radius: 12px 12px 0 0;
}

.notification-modal-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #333;
}

.notification-modal-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.btn-mark-all-read {
    background: <?= $primaryColor ?>;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-mark-all-read:hover {
    background: <?= $sidebarHoverColor ?>;
    transform: translateY(-1px);
}

.notification-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.notification-modal-close:hover {
    background: #e9ecef;
    color: #333;
}

.notification-modal-body {
    padding: 0;
    overflow-y: auto;
    flex: 1;
}

/* Ensure notification modal overlays and is visible */
.notification-modal {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.4);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.notification-modal-content {
  background: #fff;
  border-radius: 8px;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 4px 24px rgba(0,0,0,0.18);
  padding: 24px 20px 16px 20px;
  position: relative;
}
.notification-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
.notification-modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
}
.notification-modal-footer {
  text-align: right;
  margin-top: 16px;
}
.notification-modal-time {
  color: #888;
  font-size: 0.95em;
  margin-bottom: 8px;
}
</style>
</head>
<body class="tracking-page-body">
    <div class="tracking-container tracking-result-container">
        <div class="tracking-layout">
            <!-- New Icon Sidebar -->
            <div class="new-tracking-sidebar">
                <!-- Brand/Logo -->
                <div class="sidebar-brand">
                    <a href="<?= View::url('/') ?>">
                        <?php if (!empty($logoPath)): ?>
                            <img src="<?= View::e($logoPath) ?>" alt="<?= View::e($appName) ?>" style="max-height: 40px; max-width: 50px; object-fit: contain; border-radius: 8px;">
                        <?php elseif (!empty($favicon)): ?>
                            <img src="<?= View::e($favicon) ?>" alt="<?= View::e($appName) ?>" style="max-height: 40px; max-width: 50px; object-fit: contain; border-radius: 8px;">
                        <?php else: ?>
                            <div style="width: 40px; height: 40px; background: <?= $primaryColor ?>; color: white; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 18px; font-weight: bold;">
                                <?= strtoupper(substr($appName, 0, 1)) ?>
                            </div>
                        <?php endif; ?>
                    </a>
                </div>

                <!-- Main Icons -->
                <div class="sidebar-icons">
                    <div class="sidebar-icon active" data-section="tracking" title="Track Shipment">
                        <i class="fas fa-search"></i>
                        <div class="sidebar-tooltip">Track Shipment</div>
                    </div>

                    <div class="sidebar-icon" data-section="document-history" title="Documents">
                        <i class="fas fa-file-alt"></i>
                        <span class="notification-badge" id="document-count-badge" style="display: none;">0</span>
                        <div class="sidebar-tooltip">Documents</div>
                    </div>

                    <div class="sidebar-icon" data-section="notifications" title="Notifications">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge" id="notification-count-badge" style="display: none;">0</span>
                        <div class="sidebar-tooltip">Notifications</div>
                    </div>

                    <div class="sidebar-icon" data-section="track-details" title="Shipment Details">
                        <i class="fas fa-info-circle"></i>
                        <div class="sidebar-tooltip">Shipment Details</div>
                    </div>

                    <div class="sidebar-icon" data-section="map" title="Track on Map">
                        <i class="fas fa-map-marker-alt"></i>
                        <div class="sidebar-tooltip">Track on Map</div>
                    </div>

                    <div class="sidebar-icon" data-section="history" title="Tracking History">
                        <i class="fas fa-history"></i>
                        <div class="sidebar-tooltip">Tracking History</div>
                    </div>
                </div>

                <!-- Bottom Icons -->
                <div class="sidebar-bottom">
                    <div class="sidebar-icon" data-section="help" title="Help & Support">
                        <i class="fas fa-question-circle"></i>
                        <div class="sidebar-tooltip">Help & Support</div>
                    </div>

                    <div class="sidebar-icon" onclick="window.location.href='<?= App\Core\View::url('/') ?>'" title="Back to Home">
                        <i class="fas fa-home"></i>
                        <div class="sidebar-tooltip">Back to Home</div>
                    </div>
                </div>
            </div>

            <!-- Sliding Panels -->
            <div class="sidebar-panel" id="document-history-panel">
                <div class="panel-header">
                    <h3>Documents</h3>
                    <button class="panel-close" onclick="closeSidebarPanel()">&times;</button>
                </div>
                <div class="panel-content">
                    <!-- Document Tabs -->
                    <div class="document-tabs">
                        <button class="tab-button active" data-tab="pending">Pending Upload</button>
                        <button class="tab-button" data-tab="cancelled">Cancelled</button>
                        <button class="tab-button" data-tab="approved">Approved</button>
                    </div>

                    <!-- Tab Content -->
                    <div class="tab-content active" id="pending-tab">
                        <div id="pending-documents">
                            <div class="no-content">
                                <i class="fas fa-clock"></i>
                                <p>No pending documents</p>
                            </div>
                        </div>
                    </div>

                    <div class="tab-content" id="cancelled-tab">
                        <div id="cancelled-documents">
                            <div class="no-content">
                                <i class="fas fa-times-circle"></i>
                                <p>No cancelled documents</p>
                            </div>
                        </div>
                    </div>

                    <div class="tab-content" id="approved-tab">
                        <div id="approved-documents">
                            <div class="no-content">
                                <i class="fas fa-check-circle"></i>
                                <p>No approved documents</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notifications Full Modal -->
            <div class="notification-modal" id="notifications-modal">
                <div class="notification-modal-content">
                    <div class="notification-modal-header">
                        <h3>Notifications</h3>
                        <div class="notification-modal-actions">
                            <button class="btn-mark-all-read" onclick="markAllNotificationsRead()">
                                <i class="fas fa-check-double"></i> Mark All Read
                            </button>
                            <button class="notification-modal-close" onclick="closeNotificationModal()">&times;</button>
                        </div>
                    </div>
                    <div class="notification-modal-body" id="notifications-content">
                        <div class="no-content">
                            <i class="fas fa-bell"></i>
                            <p>No notifications available</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="sidebar-panel" id="track-details-panel">
                <div class="panel-header">
                    <h3>Track Details</h3>
                    <button class="panel-close" onclick="closeSidebarPanel()">&times;</button>
                </div>
                <div class="panel-content" id="track-details-content">
                    <div class="no-content">
                        <i class="fas fa-info-circle"></i>
                        <p>No tracking information available</p>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="tracking-content">
                <div class="tracking-header">
                    <h1 class="tracking-title">Tracking List</h1>
                    <p class="tracking-subtitle">Track your shipment</p>
                </div>

                <div class="tracking-main">
                    <div class="tracking-search-container">
                        <form id="tracking-form" class="tracking-form" action="<?= App\Core\View::url('/api/track') ?>" method="POST">
                            <?= App\Core\View::csrfField() ?>
                            <div class="search-input-group">
                                <i class="fas fa-search search-icon"></i>
                                <input type="text" id="tracking_number" name="tracking_number" class="search-input" placeholder="Enter tracking number..." required>
                                <button type="submit" class="search-button">
                                    <i class="fas fa-search"></i> Track
                                </button>
                            </div>
                            <p class="tracking-note">
                                <i class="fas fa-info-circle note-icon"></i>
                                <span>Please Note: Shipment information will appear approximately 8 hours after being handed in at a <?= App\Models\Setting::get('site_name', 'Courier Service') ?> location</span>
                            </p>
                        </form>
                    </div>

                    <div id="tracking-results" class="tracking-results-area">
                        <!-- Results will be loaded here via JS -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <script src="<?= App\Core\View::asset('js/public-script.js') ?>"></script>
    <script src="<?= App\Core\View::asset('js/tracking-details.js') ?>"></script>
</body>
</html>
