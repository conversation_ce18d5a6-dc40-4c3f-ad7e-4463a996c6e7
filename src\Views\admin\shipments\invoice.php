<?php
$pageTitle = 'Generate Invoice';
$breadcrumbs = [
    ['title' => 'Dashboard', 'url' => '/admin/dashboard'],
    ['title' => 'Shipments', 'url' => '/admin/shipments'],
    ['title' => 'Generate Invoice', 'url' => '']
];
?>

<div class="page-header">
    <div class="page-header-content">
        <p class="page-description">Create professional invoices for shipments</p>
    </div>
    <div class="page-actions">
        <a href="<?= App\Core\View::url('/admin/shipments') ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i>
            Back to Shipments
        </a>
    </div>
</div>

<div class="content-wrapper">
    <!-- Shipment Selection Section -->
    <div class="card mb-4 section-card section-tracking" id="shipment-selection-card">
        <div class="card-header section-header-tracking">
            <h3 class="card-title">
                <i class="fas fa-search"></i>
                Step 1: Select Shipment
            </h3>
        </div>
        <div class="card-body" id="shipment-selection-body">
            <div class="form-group">
                <label for="shipment_search">Search by Tracking Number or Client Name</label>
                <div class="input-group">
                    <input type="text" class="form-control" id="shipment_search"
                           placeholder="Enter tracking number or client name">
                    <div class="input-group-append">
                        <button type="button" class="btn btn-primary" onclick="searchShipments()">
                            <i class="fas fa-search"></i>
                            Search
                        </button>
                    </div>
                </div>
            </div>

            <div id="shipment_results" class="shipment-results" style="display: none;">
                <h5>Search Results:</h5>
                <div class="table-responsive">
                    <table class="table table-striped table-sm">
                        <thead>
                            <tr>
                                <th>Select</th>
                                <th>Tracking #</th>
                                <th>Client</th>
                                <th>Origin</th>
                                <th>Destination</th>
                                <th>Status</th>
                                <th>Cost</th>
                            </tr>
                        </thead>
                        <tbody id="shipment_results_body">
                            <!-- Results will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Selected Shipment Display -->
        <div id="selected_shipment_display" class="card-body border-top bg-light" style="display: none;">
            <h5 class="text-success">
                <i class="fas fa-check-circle"></i>
                Selected Shipment
            </h5>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>Tracking Number:</strong> <span id="selected_tracking"></span></p>
                    <p><strong>Client:</strong> <span id="selected_client"></span></p>
                </div>
                <div class="col-md-6">
                    <p><strong>Route:</strong> <span id="selected_route"></span></p>
                    <p><strong>Shipping Cost:</strong> $<span id="selected_cost"></span></p>
                </div>
            </div>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="changeShipment()">
                <i class="fas fa-edit"></i>
                Change Shipment
            </button>
        </div>
    </div>

    <!-- Invoice Configuration Section -->
    <div class="card mb-4 section-card section-client" id="invoice-config-card" style="display: none;">
        <div class="card-header section-header-client">
            <h3 class="card-title">
                <i class="fas fa-cog"></i>
                Step 2: Configure Invoice
            </h3>
        </div>
        <div class="card-body">
            <form id="invoiceForm">
                <?= App\Core\View::csrfField() ?>
                <input type="hidden" id="selected_shipment_id" name="shipment_id">

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="invoice_type">Invoice Type</label>
                            <select class="form-control" id="invoice_type" name="invoice_type" required>
                                <option value="standard">Standard Invoice</option>
                                <option value="detailed">Detailed Invoice (with package breakdown)</option>
                                <option value="commercial">Commercial Invoice (for international)</option>
                            </select>
                            <small class="form-text text-muted">
                                Standard is recommended for most shipments
                            </small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="currency">Currency</label>
                            <select class="form-control" id="currency" name="currency" required>
                                <option value="USD">USD - US Dollar ($)</option>
                                <option value="EUR">EUR - Euro (€)</option>
                                <option value="GBP">GBP - British Pound (£)</option>
                                <option value="CAD">CAD - Canadian Dollar (C$)</option>
                                <option value="AUD">AUD - Australian Dollar (A$)</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="invoice_date">Invoice Date</label>
                            <input type="date" class="form-control" id="invoice_date" name="invoice_date"
                                   value="<?= date('Y-m-d') ?>" required>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="due_date">Due Date</label>
                            <input type="date" class="form-control" id="due_date" name="due_date"
                                   value="<?= date('Y-m-d', strtotime('+30 days')) ?>" required>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="tax_rate">Tax Rate (%)</label>
                            <input type="number" class="form-control" id="tax_rate" name="tax_rate"
                                   value="0" min="0" max="100" step="0.01" required>
                        </div>
                    </div>
                </div>

                <!-- Additional Charges Section -->
                <div class="form-group">
                    <label>Additional Charges (Optional)</label>
                    <div id="additional_charges_container">
                        <!-- Charges will be added here -->
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="addCharge()">
                        <i class="fas fa-plus"></i>
                        Add Additional Charge
                    </button>
                </div>

                <div class="form-group">
                    <label for="notes">Invoice Notes (Optional)</label>
                    <textarea class="form-control" id="notes" name="notes" rows="3"
                              placeholder="Add any special notes or payment terms"></textarea>
                </div>
            </form>
        </div>
    </div>

    <!-- Invoice Actions Section -->
    <div class="card section-card section-details" id="invoice-actions-card" style="display: none;">
        <div class="card-header section-header-details">
            <h3 class="card-title">
                <i class="fas fa-file-invoice"></i>
                Step 3: Generate Invoice
            </h3>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h5 class="mb-3">Invoice Summary</h5>
                    <div id="invoice-summary" class="invoice-summary-details">
                        <div class="summary-row">
                            <span class="summary-label">Shipment:</span>
                            <span class="summary-value" id="summary-tracking">-</span>
                        </div>
                        <div class="summary-row">
                            <span class="summary-label">Client:</span>
                            <span class="summary-value" id="summary-client">-</span>
                        </div>
                        <div class="summary-row">
                            <span class="summary-label">Base Cost:</span>
                            <span class="summary-value">$<span id="summary-base-cost">0.00</span></span>
                        </div>
                        <div class="summary-row">
                            <span class="summary-label">Additional Charges:</span>
                            <span class="summary-value">$<span id="summary-additional">0.00</span></span>
                        </div>
                        <div class="summary-row">
                            <span class="summary-label">Tax:</span>
                            <span class="summary-value">$<span id="summary-tax">0.00</span></span>
                        </div>
                        <div class="summary-divider"></div>
                        <div class="summary-row summary-total">
                            <span class="summary-label">Total Amount:</span>
                            <span class="summary-value">$<span id="summary-total">0.00</span></span>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <h5>Actions</h5>
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-primary btn-lg" onclick="generateInvoice()">
                            <i class="fas fa-eye"></i>
                            Preview Invoice
                        </button>
                        <button type="button" class="btn btn-success btn-lg" onclick="generateAndDownload()">
                            <i class="fas fa-download"></i>
                            Generate & Download PDF
                        </button>
                        <button type="button" class="btn btn-info" onclick="emailInvoice()">
                            <i class="fas fa-envelope"></i>
                            Email to Client
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Invoice Preview Modal -->
<div id="invoicePreviewModal" class="modal fade" tabindex="-1" role="dialog" style="z-index: 99999;">
    <div class="modal-dialog" role="document" style="max-width: 95vw; width: 900px; margin: 1rem auto;">
        <div class="modal-content" style="height: 95vh; display: flex; flex-direction: column;">
            <div class="modal-header" style="flex-shrink: 0; background: #f8f9fa; border-bottom: 2px solid #e9ecef;">
                <h5 class="modal-title" style="color: #2c3e50; font-weight: 600;">
                    <i class="fas fa-file-invoice"></i> Invoice Preview
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" style="font-size: 1.5rem; opacity: 0.8;">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="flex: 1; overflow-y: auto; padding: 0;">
                <div id="invoice_preview_content" style="height: 100%; display: flex; align-items: center; justify-content: center; padding: 20px;">
                    <!-- Invoice preview will be loaded here -->
                </div>
            </div>
            <div class="modal-footer" style="flex-shrink: 0; background: #f8f9fa; border-top: 2px solid #e9ecef;">
                <button type="button" class="btn btn-light" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="downloadInvoicePDF()">
                    <i class="fas fa-download"></i>
                    Download PDF
                </button>
                <button type="button" class="btn btn-success" onclick="emailInvoice()">
                    <i class="fas fa-envelope"></i>
                    Email Invoice
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
}

.page-header-content h1 {
    margin: 0;
    color: var(--primary-color);
}

.page-header-content p {
    margin: 5px 0 0 0;
    color: #6c757d;
}

.card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: none;
    margin-bottom: 20px;
}

.card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 15px 20px;
    border-radius: 8px 8px 0 0;
}

.card-title {
    margin: 0;
    color: var(--text-color);
    font-size: 16px;
    font-weight: 600;
}

.card-body {
    padding: 20px;
}

.shipment-results {
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
}

.selected-shipment {
    margin-top: 20px;
}

.charge-item {
    margin-bottom: 10px;
}

.form-actions {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.info-item {
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.info-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.info-item ul {
    margin-bottom: 0;
    padding-left: 20px;
}

.info-item li {
    margin-bottom: 5px;
}

.btn-group {
    margin-right: 10px;
}

.table-sm th,
.table-sm td {
    padding: 8px;
}

.alert {
    margin-bottom: 0;
}

/* Invoice Preview Modal Styles */
#invoicePreviewModal .modal-dialog {
    max-width: 95vw !important;
    width: 900px !important;
    margin: 1rem auto !important;
}

#invoicePreviewModal .modal-content {
    height: 95vh !important;
    display: flex !important;
    flex-direction: column !important;
}

#invoicePreviewModal .modal-header {
    flex-shrink: 0 !important;
    background: #f8f9fa !important;
    border-bottom: 2px solid #e9ecef !important;
}

#invoicePreviewModal .modal-body {
    flex: 1 !important;
    overflow-y: auto !important;
    padding: 0 !important;
}

#invoicePreviewModal .modal-footer {
    flex-shrink: 0 !important;
    background: #f8f9fa !important;
    border-top: 2px solid #e9ecef !important;
}

#invoice_preview_content {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    min-height: 600px;
}

/* Invoice Content Styling */
#invoice_preview_content .invoice {
    width: 100% !important;
    max-width: 800px !important;
    margin: 0 auto !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
    border-radius: 8px !important;
    overflow: hidden !important;
}

/* Responsive adjustments for smaller screens */
@media (max-width: 768px) {
    #invoicePreviewModal .modal-dialog {
        width: 98vw !important;
        margin: 0.5rem auto !important;
    }

    #invoicePreviewModal .modal-content {
        height: 98vh !important;
    }

    #invoice_preview_content {
        padding: 10px;
    }

    #invoice_preview_content .invoice {
        font-size: 12px !important;
    }
}

/* Section-specific styling with fainted backgrounds */
.section-card {
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.section-tracking {
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.05) 0%, rgba(52, 152, 219, 0.02) 100%);
    border-left: 1px solid #3498db;
}

.section-header-tracking {
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.1) 0%, rgba(52, 152, 219, 0.05) 100%);
    border-bottom: 1px solid rgba(52, 152, 219, 0.2);
}

.section-client {
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.05) 0%, rgba(46, 204, 113, 0.02) 100%);
    border-left: 1px solid #2ecc71;
}

.section-header-client {
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.1) 0%, rgba(46, 204, 113, 0.05) 100%);
    border-bottom: 1px solid rgba(46, 204, 113, 0.2);
}

.section-details {
    background: linear-gradient(135deg, rgba(155, 89, 182, 0.05) 0%, rgba(155, 89, 182, 0.02) 100%);
    border-left: 1px solid #9b59b6;
}

.section-header-details {
    background: linear-gradient(135deg, rgba(155, 89, 182, 0.1) 0%, rgba(155, 89, 182, 0.05) 100%);
    border-bottom: 1px solid rgba(155, 89, 182, 0.2);
}

.section-card .card-title {
    color: #2c3e50;
    font-weight: 600;
    margin: 0;
}

.section-card .card-title i {
    margin-right: 8px;
    opacity: 0.8;
}

.section-tracking .card-title i {
    color: #3498db;
}

.section-client .card-title i {
    color: #2ecc71;
}

.section-details .card-title i {
    color: #9b59b6;
}

/* Enhanced form styling within sections */
.section-card .form-group label {
    font-weight: 500;
    color: #34495e;
    margin-bottom: 6px;
}

.section-card .form-control {
    border-radius: 6px;
    border: 1px solid #e0e6ed;
    transition: all 0.3s ease;
}

.section-card .form-control:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.15);
}

/* Selected shipment display styling */
#selected_shipment_display {
    background: linear-gradient(135deg, rgba(39, 174, 96, 0.08) 0%, rgba(39, 174, 96, 0.03) 100%);
    border: 1px solid rgba(39, 174, 96, 0.2);
    border-radius: 8px;
    margin-top: 15px;
}

#selected_shipment_display h5 {
    color: #27ae60;
    font-weight: 600;
}

/* Summary section styling */
.invoice-summary {
    background: linear-gradient(135deg, rgba(52, 73, 94, 0.05) 0%, rgba(52, 73, 94, 0.02) 100%);
    border: 1px solid rgba(52, 73, 94, 0.1);
    border-radius: 8px;
    padding: 20px;
}

.invoice-summary h5 {
    color: #2c3e50;
    border-bottom: 2px solid #ecf0f1;
    padding-bottom: 10px;
    margin-bottom: 15px;
}

/* Improved invoice summary details styling */
.invoice-summary-details {
    background: #ffffff;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #e9ecef;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f8f9fa;
}

.summary-row:last-child {
    border-bottom: none;
}

.summary-label {
    font-weight: 500;
    color: #495057;
    font-size: 14px;
}

.summary-value {
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
}

.summary-divider {
    height: 1px;
    background: #dee2e6;
    margin: 15px 0;
}

.summary-total {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.05) 100%);
    border-radius: 6px;
    padding: 15px 12px;
    margin: 10px 0 0 0;
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.summary-total .summary-label,
.summary-total .summary-value {
    font-size: 16px;
    font-weight: 700;
    color: #28a745;
}
</style>

<script>
let selectedShipmentData = null;

document.addEventListener('DOMContentLoaded', function() {
    // Check if shipment is preselected from controller or URL parameters
    <?php if (isset($preselectedShipment) && $preselectedShipment): ?>
    // Pre-populate with the preselected shipment
    loadShipmentById(<?= $preselectedShipment['id'] ?>);
    <?php else: ?>
    // Check if shipment_id is provided in URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const shipmentId = urlParams.get('shipment_id');

    if (shipmentId) {
        // Pre-populate with the specified shipment
        loadShipmentById(shipmentId);
    }
    <?php endif; ?>

    // Add event listeners for form changes
    document.getElementById('tax_rate').addEventListener('input', updateInvoiceSummary);
    document.getElementById('invoice_type').addEventListener('change', updateInvoiceSummary);

    // Initialize modal close functionality
    initializeModalHandlers();
});

function initializeModalHandlers() {
    // Close modal handlers
    const closeBtns = document.querySelectorAll('[data-dismiss="modal"]');
    closeBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const modal = this.closest('.modal');
            if (modal) {
                closeModal(modal);
            }
        });
    });

    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target.classList.contains('modal')) {
            closeModal(event.target);
        }
    });
}

function closeModal(modal) {
    modal.style.display = 'none';
    modal.classList.remove('show');
    document.body.classList.remove('modal-open');

    // Remove backdrop if exists
    const backdrop = document.querySelector('.modal-backdrop');
    if (backdrop) {
        backdrop.remove();
    }
}

function loadShipmentById(shipmentId) {
    fetch(`/courier/public/api/admin/shipments/get.php?id=${shipmentId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.shipment) {
                const shipment = data.shipment;
                selectedShipmentData = shipment;

                // Update selected shipment display
                document.getElementById('selected_shipment_id').value = shipment.id;
                document.getElementById('selected_tracking').textContent = shipment.tracking_number;
                document.getElementById('selected_client').textContent = shipment.sender_name || shipment.shipper_name || 'N/A';
                document.getElementById('selected_route').textContent = `${shipment.origin} → ${shipment.destination}`;
                document.getElementById('selected_cost').textContent = parseFloat(shipment.shipping_cost || 0).toFixed(2);

                // Show selected shipment and proceed to next steps
                showSelectedShipment();

                // Hide search section since we have pre-selected shipment
                document.getElementById('shipment-selection-body').style.display = 'none';
                document.querySelector('#shipment-selection-card .card-header h3').innerHTML =
                    '<i class="fas fa-check-circle text-success"></i> Step 1: Shipment Pre-Selected';
            } else {
                console.error('Error loading shipment:', data.message);
                alert('Error loading shipment: ' + (data.message || 'Shipment not found'));
            }
        })
        .catch(error => {
            console.error('Error loading shipment:', error);
            alert('An error occurred while loading the shipment.');
        });
}

function showSelectedShipment() {
    document.getElementById('selected_shipment_display').style.display = 'block';
    document.getElementById('invoice-config-card').style.display = 'block';
    document.getElementById('invoice-actions-card').style.display = 'block';
    updateInvoiceSummary();
}

function changeShipment() {
    document.getElementById('shipment-selection-body').style.display = 'block';
    document.getElementById('selected_shipment_display').style.display = 'none';
    document.getElementById('invoice-config-card').style.display = 'none';
    document.getElementById('invoice-actions-card').style.display = 'none';
    document.querySelector('#shipment-selection-card .card-header h3').innerHTML =
        '<i class="fas fa-search"></i> Step 1: Select Shipment';
    selectedShipmentData = null;
}

function searchShipments() {
    const searchTerm = document.getElementById('shipment_search').value.trim();

    if (!searchTerm) {
        alert('Please enter a tracking number or client name to search');
        return;
    }

    fetch(`<?= App\Core\View::url('/api/admin/shipments/search.php') ?>?q=${encodeURIComponent(searchTerm)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayShipmentResults(data.shipments);
            } else {
                alert('Error searching shipments: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error searching shipments:', error);
            alert('An error occurred while searching shipments.');
        });
}

function displayShipmentResults(shipments) {
    const resultsContainer = document.getElementById('shipment_results');
    const resultsBody = document.getElementById('shipment_results_body');
    
    resultsBody.innerHTML = '';
    
    if (shipments.length === 0) {
        resultsBody.innerHTML = '<tr><td colspan="7" class="text-center">No shipments found</td></tr>';
    } else {
        shipments.forEach(shipment => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <button class="btn btn-sm btn-primary" onclick="selectShipment(${shipment.id}, '${shipment.tracking_number}', '${shipment.client_name || 'N/A'}', '${shipment.origin}', '${shipment.destination}', '${shipment.shipping_cost || 0}')">
                        Select
                    </button>
                </td>
                <td>${shipment.tracking_number}</td>
                <td>${shipment.client_name || 'N/A'}</td>
                <td>${shipment.origin}</td>
                <td>${shipment.destination}</td>
                <td><span class="badge badge-${getStatusColor(shipment.status)}">${shipment.status}</span></td>
                <td>$${parseFloat(shipment.shipping_cost || 0).toFixed(2)}</td>
            `;
            resultsBody.appendChild(row);
        });
    }
    
    resultsContainer.style.display = 'block';
}

function selectShipment(id, tracking, client, origin, destination, cost) {
    selectedShipmentData = {
        id: id,
        tracking_number: tracking,
        client_name: client,
        origin: origin,
        destination: destination,
        shipping_cost: cost
    };

    document.getElementById('selected_shipment_id').value = id;
    document.getElementById('selected_tracking').textContent = tracking;
    document.getElementById('selected_client').textContent = client;
    document.getElementById('selected_route').textContent = `${origin} → ${destination}`;
    document.getElementById('selected_cost').textContent = parseFloat(cost).toFixed(2);

    showSelectedShipment();
    document.getElementById('shipment_results').style.display = 'none';
}

function updateInvoiceSummary() {
    if (!selectedShipmentData) return;

    const baseCost = parseFloat(selectedShipmentData.shipping_cost || 0);
    const taxRate = parseFloat(document.getElementById('tax_rate').value || 0);

    // Calculate additional charges
    let additionalCharges = 0;
    const chargeAmounts = document.querySelectorAll('input[name="charge_amount[]"]');
    chargeAmounts.forEach(input => {
        const amount = parseFloat(input.value || 0);
        additionalCharges += amount;
    });

    const subtotal = baseCost + additionalCharges;
    const tax = subtotal * (taxRate / 100);
    const total = subtotal + tax;

    // Update summary display
    document.getElementById('summary-tracking').textContent = selectedShipmentData.tracking_number;
    document.getElementById('summary-client').textContent = selectedShipmentData.client_name || 'N/A';
    document.getElementById('summary-base-cost').textContent = baseCost.toFixed(2);
    document.getElementById('summary-additional').textContent = additionalCharges.toFixed(2);
    document.getElementById('summary-tax').textContent = tax.toFixed(2);
    document.getElementById('summary-total').textContent = total.toFixed(2);
}

function addCharge() {
    const container = document.getElementById('additional_charges_container');
    const chargeItem = document.createElement('div');
    chargeItem.className = 'charge-item mb-3';
    chargeItem.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <input type="text" class="form-control" name="charge_description[]"
                       placeholder="Description (e.g., Insurance, Handling Fee)">
            </div>
            <div class="col-md-4">
                <input type="number" class="form-control" name="charge_amount[]"
                       placeholder="Amount" step="0.01" oninput="updateInvoiceSummary()">
            </div>
            <div class="col-md-2">
                <button type="button" class="btn btn-danger btn-sm" onclick="removeCharge(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;
    container.appendChild(chargeItem);
}

function removeCharge(button) {
    button.closest('.charge-item').remove();
    updateInvoiceSummary();
}

function generateInvoice() {
    if (!selectedShipmentData) {
        alert('Please select a shipment first');
        return;
    }

    const formData = new FormData(document.getElementById('invoiceForm'));

    fetch('<?= App\Core\View::url('/api/admin/invoices/generate.php') ?>', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('invoice_preview_content').innerHTML = data.html;
            showModal('invoicePreviewModal');
        } else {
            alert('Error generating invoice: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error generating invoice:', error);
        alert('An error occurred while generating the invoice.');
    });
}

function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'block';
        modal.classList.add('show');
        document.body.classList.add('modal-open');

        // Add backdrop
        const backdrop = document.createElement('div');
        backdrop.className = 'modal-backdrop fade show';
        document.body.appendChild(backdrop);
    }
}

function generateAndDownload() {
    if (!selectedShipmentData) {
        alert('Please select a shipment first');
        return;
    }
    
    const formData = new FormData(document.getElementById('invoiceForm'));
    formData.append('download', '1');
    
    fetch('<?= App\Core\View::url('/api/admin/invoices/generate.php') ?>', {
        method: 'POST',
        body: formData
    })
    .then(response => response.blob())
    .then(blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `invoice-${selectedShipmentData.tracking_number}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
    })
    .catch(error => {
        console.error('Error downloading invoice:', error);
        alert('An error occurred while downloading the invoice.');
    });
}

function downloadInvoicePDF() {
    // Implementation for downloading PDF from preview
    generateAndDownload();
}

function emailInvoice() {
    // Implementation for emailing invoice
    alert('Email functionality will be implemented in the next phase');
}



function getStatusColor(status) {
    const colors = {
        'pending': 'warning',
        'in_transit': 'info',
        'delivered': 'success',
        'delayed': 'danger',
        'cancelled': 'secondary'
    };
    return colors[status] || 'secondary';
}
</script>
